services:

  ####################################################################################################
  ###===================================  以下为中间件模块   =========================================###
  ####################################################################################################



  appnginx:
    container_name: appnginx
    image: nginx:stable-alpine-perl
    hostname: "appnginx"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - /docker/nginx/extra:/etc/nginx/extra
      - /docker/nginx/logs/:/var/log/nginx
      - /docker/nginx/html:/etc/nginx/html
      - /docker/nginx/cache:/etc/nginx/cache
    privileged: true
    restart: always
    networks:
      - ngnet

  redis:
    container_name: redis
    image: redis:6.2.14-alpine
    hostname: "redis"
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/redis/data:/data
    command: "redis-server --appendonly yes --requirepass redis2root88987 --notify-keyspace-events Ex"
    privileged: true
    restart: always
    network_mode: host


  minio:
    image: minio/minio:RELEASE.2022-05-08T23-50-31Z
    container_name: minio
    restart: always
    privileged: true
    command: server /data --console-address ":9001"
    environment:
      - TZ=${TZ}
      - MINIO_ROOT_USER=minioroot
      - MINIO_ROOT_PASSWORD=minio2root528
    read_only: true
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/minio/data:/data
      - /docker/minio/config:/root/.minio
    network_mode: host

  ####################################################################################################
  ###===================================   以下为数据源模块   =========================================###
  ####################################################################################################


  mysql:
    container_name: mysql
    image: mysql:8.0.38
    restart: always
    privileged: true
    user: root
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
    environment:
      - TZ=${TZ}
      - MYSQL_ROOT_PASSWORD=mysql887root588
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/mysql/data:/var/lib/mysql:rw
      - /docker/mysql/conf/my.cnf:/etc/mysql/my.cnf:rw
      - /docker/mysql/log:/var/log/mysql:rw
      - /docker/mysql/mysql-files:/var/lib/mysql-files:rw
    network_mode: host

  ####################################################################################################
  ###=================================  以下为billow服务模块  =======================================###
  ####################################################################################################

  bill-server:
    container_name: bill-server
    image: bill-server:latest
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/bill/logs/:/bill/logs/
      - /docker/bill/:/bill/
    privileged: true
    network_mode: host
    environment:
      - TZ=${TZ}
    build:
      context: bill
      dockerfile: Dockerfile

  bill-server-new:
    container_name: bill-server-new
    image: bill-server-new:latest
    volumes:
      - /etc/localtime:/etc/localtime
      - /docker/billnew/logs/:/billnew/logs/
      - /docker/billnew/:/billnew/
    privileged: true
    network_mode: host
    environment:
      - TZ=${TZ}
    build:
      context: billnew
      dockerfile: Dockerfile
networks:
  ngnet:
    driver: bridge
