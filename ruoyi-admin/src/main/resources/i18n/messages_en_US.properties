frame.work.core.success=Success
frame.work.core.fail=Fail
frame.work.core.fail.success=Successful operation
frame.work.core.common.notNull={0}Cannot be empty

#错误消息
not.null=* Mandatory
user.jcaptcha.error=CAPTCHA code error
user.jcaptcha.expire=Captcha is no longer valid
user.not.exists=User doesn't exist/wrong password
user.password.not.match=User doesn't exist/wrong password
user.password.retry.limit.count=Wrong password input {0} times
user.password.retry.limit.exceed=Account lockout {1} minute for {0} incorrect password entries
user.password.delete=Sorry, your account has been deleted
user.blocked=User has been banned, please contact an administrator
role.blocked=Character has been banned, please contact an administrator
login.blocked=Unfortunately, the access IP has been blacklisted from the system
user.logout.success=Exit successful

length.not.valid=The length must be between {min} and {max} characters.

user.username.not.valid=* :: Composed of 2 to 20 Chinese characters, letters, numbers or underscores and must begin with a non-numeric character
user.password.not.valid=* :: 5-50 characters

user.email.not.valid=Mailbox format error
user.mobile.phone.number.not.valid=Wrong format of cell phone number
user.login.success=Login Successful
user.register.success=Register Successfully
user.notfound=Please re-login
user.forcelogout=Administrator forced to log out, please log in again
user.unknown.error=Unknown error, please re-login

##文件上传消息
upload.exceed.maxSize=Uploaded file size exceeds the restricted file size! <br/>The maximum allowed file size is: {0}MB!
upload.filename.exceed.length=Maximum {0} characters for uploaded filenames

##权限
no.permission=You do not have permissions for the data, please contact an administrator to add them [{0}].
no.create.permission=You do not have permission to create data, please contact an administrator to add permission [{0}].
no.update.permission=You do not have permission to modify data, please contact the administrator to add permission [{0}].
no.delete.permission=You do not have permission to delete data, please contact an administrator to add permission [{0}].
no.export.permission=You do not have permission to export data, please contact administrator to add permission [{0}].
no.view.permission=You do not have permission to view the data, please contact the administrator to add permission [{0}].


#bill
custom.login.status.notActive=Login account is not activated, please contact the administrator.
custom.login.notExists=User does not exist.
custom.register.usernameExist=Account {0} already exists.
custom.register.phoneExist=Cell phone number {0} already exists.
custom.register.username.Exist=username already exists.
custom.register.usernameEmpty=Please fill in the account number.
custom.register.passwordEmpty=Please fill in the password.
custom.register.serviceTeamIdEmpty=Please select a customer service group.
custom.register.customIdEmpty=Client ID cannot be null.
custom.register.phoneNotAllow=Cell phone registration is not supported.
custom.register.usernameNotAllow=User name registration is not supported.
custom.register.phoneEmpty=The cell phone number cannot be empty.
custom.register.phoneCodeEmpty=The area code cannot be empty.
custom.register.levelEmpty=VIP level cannot be empty.
custom.register.inviteCodeInvalid=Invalid Invitation Code.
custom.register.allowInviteFalse=This user does not have invite privileges.
custom.register.registerCountMax=The number of registrations exceeds the limit.
custom.update.customIdEmpty=Please select a member.
custom.guard.guardJsonEmpty=Balance Guarantee json cannot be empty.
custom.amount.illegalAmount=Illegal amount.
custom.amount.balanceEmpty=The amount cannot be empty.
custom.amount.remarkEmpty=Note cannot be empty.
custom.amount.insufficientBalance=Insufficient balance.
custom.deposit.unfreezeTimeNotCome=It's not time to thaw.
rule.add.ruleKeyRepeat=Key conflict, please don't add it twice.

## ==== bill api info start===
balance.not.enough=Insufficient balance
trade.day.of.do.order.limit=The number of daily transactions is capped.
number.of.orders.today.limit=The order has been completed, please contact Customer service.
order.is.sold.out=There is a discrepancy between the account level and the balance, and the system is unable to match orders.
gold.egg.not.null=The spin to win cannot be empty.
withdraw.number.limit=The maximum number of withdrawals has been reached.
withdraw.less.order.total.limit=The current singular is less than the singular withdrawal requirement.
you.have.signed.in.today=You have signed in today.
sign.config.is.null=The sign-in configuration is empty. Please contact the administrator.
reached.daily.order.limit=No sign-in needs to be singular.
the.order.does.not.exist=Order does not exist.
the.order.has.been.processed=Order processed, please refresh the page to see the latest data.
advanced.order.error.msg=You got a Elite Order.Please contact Customer Service.
luxury.order.error.msg=You got a Luxury Order.Please contact Customer Service.
gold.egg.error.msg=You got a Golden Egg Order.Please contact Customer Service.
common.order.error.msg=Congratulation! You got a Standard Suite. Please contact User Support for further information to complete the purchase.
order.no.cannot.be.empty=The order number cannot be empty.
bank.card.not.bind=Please bind your bank card first.
not.set.password=Please enter your withdrawal password.
withdraw.password.not.true=The withdrawal password is incorrect.
withdraw.amount.out.range=The withdrawal amount is out of range.
injection.is.null=No injection order found.
injection.status.not.change=The current injection order status cannot be changed.
the.recharge.order.does.not.exist=No top-up order found.
order.type.is.null=The order type cannot be empty.
staring.order.not.null=Please play a game first.
injection.product.not.null=The injection order number cannot be empty.
commission.ratio.not.null=The commission ratio cannot be empty.
commission.multiple.not.null=Commission multiply The multiple cannot be empty
injection.status.not.cancel=The current injection order status cannot be cancelled
can.not.draw=You can't draw,please contact customer service.
bank.ard.has.been.bound=This bank card has been bound. Please try to change a new bank card.
custom.password.error=The password is incorrect. Please try again.
product.is.locked=Inventory operation busy, please try again later.
product.not.exists=No integral information is found.
product.inventory.not.enough=There are not enough goods in stock to complete the exchange.
user.account.is.locked=User account is busy, please try again later.
user.points.not.enough=Not enough points to complete the redemption.
user.account.is.locked2=User account operation is interrupted.
product.is.locked2=Merchandise inventory operations are interrupted.
withdrawal.proceeding=Your current withdrawal is under review. Please contact user support for assistance before proceeding with next withdrawal
existed.order.not.null=The user currently has an odd number of injection orders.
recharge.financial.error=The account has not been recharged. You can purchase financial products only after the recharge is successful.
allowed.withdrawal.total.amount=The total amount of withdrawals is limited.
email.format.error=Email format error.
custom.modifyUsername.time.limit=The number of times to modify the account number is limited,It can only be modified once within a year.
current.level.not.do.order=The current member level cannot participate in the group purchase.
financial.order.error=The experience fund can only be used to purchase one financial product.
bill.purchase.goods.record.purchaseGoodsId.empty=The purchase goods ID cannot be empty.
purchase.goods.record.limit=The purchase limit has been reached.
pay.amount.out.range=The payment amount is less of goods price.
pre.can.not.draw=Sorry,you can't Place order,You can contact customer service for help.
custom.register.google.Exist=current email already exists. please change another email.
custom.register.emailEmpty=Please enter your email.
custom.register.passwordError=The password is incorrect. Please try again.
custom.register.cannotBindSelf=You can't bind your own account.
prize.draw.chances.not.enough=You don't have enough chances to draw.
prize.not.exists=The prize does not exist.
prize.already.drawn=You have already drawn this prize.
redemption.code.not.exists=The redemption code does not exist.
redemption.code.used.up=The redemption code has been used up.
custom.register.passwordNotMatch=The new password is inconsistent with the confirmed password
custom.register.emailExist=The email:{0} already exists.
custom.register.oldPasswordError=The old password is incorrect. Please try again.
## ==== bill api info end===

