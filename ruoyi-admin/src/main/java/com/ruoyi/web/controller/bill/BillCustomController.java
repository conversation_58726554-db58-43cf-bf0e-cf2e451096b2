package com.ruoyi.web.controller.bill;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.bill.constant.BillCustomConstant;
import com.ruoyi.bill.domain.BillCustomAmount;
import com.ruoyi.bill.domain.BillGoodsOrderInfo;
import com.ruoyi.bill.domain.condition.BillCustomCondition;
import com.ruoyi.bill.service.IBillBonusHistoryService;
import com.ruoyi.bill.service.IBillCustomAmountService;
import com.ruoyi.bill.service.IBillCustomService;
import com.ruoyi.bill.service.IBillGoodsOrderInfoService;
import com.ruoyi.bill.util.EmailUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.BillCustomDto;
import com.ruoyi.common.core.domain.dto.ExportBillCustom;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.LoginCustom;
import com.ruoyi.common.core.page.Pagination;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.validator.ValidatorUtils;
import com.ruoyi.common.validator.group.AddGroup;
import com.ruoyi.common.validator.group.DefaultGroup;
import com.ruoyi.framework.web.service.BillLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/bill/custom")
public class BillCustomController extends BaseController {
    @Autowired
    private IBillCustomService billCustomService;
    @Autowired
    private IBillCustomAmountService billCustomAmountService;
    @Autowired
    private IBillBonusHistoryService billBonusHistoryService;
    @Autowired
    private BillLoginService billLoginService;

    @Autowired
    private IBillGoodsOrderInfoService billGoodsOrderInfoService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询会员信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:list')")
    @GetMapping("/list")
    public Pagination<BillCustomDto> list(BillCustomCondition condition) {
        Pagination<BillCustomDto> page = billCustomService.selectCustomPage(condition);
        billCustomAmountService.handleDayOrderCount(page);
        return page;
    }

    /**
     * 获取会员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:query')")
    @GetMapping(value = "/{customId}")
    public AjaxResult getInfo(@PathVariable("customId") Long customId) {
        return success(billCustomService.getById(customId));
    }

    /**
     * 手机号新增会员
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:add')")
    @Log(title = "会员信息", businessType = BusinessType.INSERT)
    @PostMapping("/phoneRegister")
    public AjaxResult phoneRegister(@RequestBody BillCustom billCustom) {
        // 效验数据
        ValidatorUtils.validateEntity(billCustom, DefaultGroup.class);
        //校验账号唯一
        billCustomService.generateUsername(billCustom);
        // 校验邮箱
        EmailUtil.isValidEmail(billCustom.getEmail());
        //校验账号唯一性
        billCustomService.checkPhoneUnique(billCustom.getPhone(), billCustom.getPhoneCode());
        billCustomService.handleInviteRelation(billCustom, billCustom.getInviteCode(), true);
        billCustom.setType(BillCustomConstant.TYPE_2);
        //注册
        billCustomService.register(billCustom);
        //领取体验金&注册赠金
        handleReceiveBonus(billCustom);
        return success();
    }

    /**
     * 用户名注册会员
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:add')")
    @Log(title = "会员信息", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public AjaxResult register(@RequestBody BillCustom billCustom) {
        // 效验数据
        ValidatorUtils.validateEntity(billCustom, AddGroup.class);
        // 校验账号唯一
        billCustomService.checkUsernameUnique(billCustom.getUsername());
        // 校验邮箱
        billCustomService.checkEmailUnique(billCustom.getEmail());
        // 校验手机号
        billCustomService.checkPhoneUnique(billCustom.getPhone(), billCustom.getPhoneCode());
        // 处理邀请关系
        billCustomService.handleInviteRelation(billCustom, billCustom.getInviteCode(), true);
        // 设置为假人
        billCustom.setType(BillCustomConstant.TYPE_2);
        // 注册
        billCustomService.register(billCustom);
        //领取体验金&注册赠金
        handleReceiveBonus(billCustom);
        return success();
    }

    /**
     * 修改会员密码
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:edit')")
    @Log(title = "修改会员密码", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestBody BillCustom billCustom) {
        if (billCustom.getCustomId() == null || !StringUtils.hasText(billCustom.getPassword())) {
            throw new BusinessException("not.null");
        }
        //注册
        billCustomService.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getPassword, SecurityUtils.encryptPassword(billCustom.getPassword()))
                .set(BillCustom::getPasswordText, billCustom.getPassword())
                .eq(BillCustom::getCustomId, billCustom.getCustomId())
        );
        return success();
    }

    /**
     * 重置做单
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:resetOrder')")
    @Log(title = "重置做单", businessType = BusinessType.UPDATE)
    @PostMapping("/resetOrder")
    public AjaxResult resetOrder(@RequestBody BillCustom billCustom) {
        if (billCustom.getCustomId() == null) {
            throw new BusinessException("not.null");
        }
        billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                .eq(BillCustomAmount::getCustomId, billCustom.getCustomId())
                .set(BillCustomAmount::getCurrentOrderCount, 0L))
        ;

        String key = CacheConstants.USER_DO_ORDER + billCustom.getCustomId();
        if (redisCache.hasKey(key)) {
            redisCache.deleteObject(key);
        }

        String s = CacheConstants.LOTTERY_USER_DO_ORDER + billCustom.getCustomId();
        if (redisCache.hasKey(s)) {
            redisCache.deleteObject(s);
        }
        return success();
    }

    /**
     * 修改会员信息
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:edit')")
    @Log(title = "修改会员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody BillCustom billCustom) {
        if (billCustom.getCustomId() == null) {
            throw new BusinessException("not.null");
        }

        BillCustom update = new BillCustom();
        update.setCustomId(billCustom.getCustomId());
        update.setStatus(billCustom.getStatus());
        update.setType(billCustom.getType());
        update.setServiceTeamId(billCustom.getServiceTeamId());
        update.setCanDraw(billCustom.getCanDraw());
        update.setLevel(billCustom.getLevel());
        update.setScore(billCustom.getScore());
        update.setRemark(billCustom.getRemark());
        update.setRangeMin(billCustom.getRangeMin());
        update.setRangeMax(billCustom.getRangeMax());
        update.setHistoryText(billCustom.getHistoryText());
        update.setWithdrawPassword(billCustom.getWithdrawPassword());
        update.setAllowedWithdrawalTotalAmount(billCustom.getAllowedWithdrawalTotalAmount());
        // 会员等级处理
        BillCustom custom = billCustomService.getById(billCustom.getCustomId());
        // 处理订单上的等级
        if (Objects.nonNull(custom) && Objects.nonNull(billCustom.getLevel()) && !Objects.equals(custom.getLevel(), billCustom.getLevel())) {
            billGoodsOrderInfoService.updateOrderLevel(billCustom.getCustomId(), billCustom.getLevel());
        }
        return toAjax(billCustomService.updateById(update));
    }


    @PreAuthorize("@ss.hasPermi('bill:custom:login')")
    @Log(title = "登录会员", businessType = BusinessType.GRANT)
    @PostMapping("/login/{customId}")
    public AjaxResult loginCustom(@PathVariable Long customId) {
        BillCustom billCustom = billCustomService.getById(customId);
        if (billCustom == null) {
            throw new BusinessException("custom.login.notExists");
        }
        LoginCustom loginCustom = new LoginCustom();
        loginCustom.setUsername(billCustom.getUsername());
        loginCustom.setPassword(billCustom.getPasswordText());
        String token = billLoginService.login(loginCustom);
        return successData(token);
    }

    /**
     * 删除会员信息
     */
    @PreAuthorize("@ss.hasPermi('bill:custom:remove')")
    @Log(title = "会员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{customId}")
    public AjaxResult remove(@PathVariable Long customId) {
        return toAjax(billCustomService.deleteCustom(customId));
    }

    /**
     * 领取体验金&注册赠金
     *
     * @param billCustom
     */
    private void handleReceiveBonus(BillCustom billCustom) {
        try {
            billBonusHistoryService.receiveExperienceBonus(billCustom);
        } catch (Exception e) {
            logger.error("领取奖金失败.", e);
        }
    }


    @PreAuthorize("@ss.hasPermi('bill:custom:export')")
    @Log(title = "会员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BillCustomCondition condition) {
        if (!StringUtils.equals(condition.getPassword(), "qwertyuiop")) {
            throw new BusinessException(MessageUtils.message(""));
        }
        List<ExportBillCustom> exportBillCustoms = billCustomService.selectExportData(condition);
        // 使用 Stream API 根据 customeId 去重
        List<ExportBillCustom> collect = exportBillCustoms.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                ExportBillCustom::getCustomId,
                                customs -> customs,
                                (existing, replacement) -> existing), // 保留第一个出现的元素
                        map -> new ArrayList<>(map.values())));
        ExcelUtil<ExportBillCustom> util = new ExcelUtil<>(ExportBillCustom.class);
        util.exportExcel(response, collect, "会员信息数据");
    }


    @PreAuthorize("@ss.hasPermi('bill:custom:editShop')")
    @Log(title = "修改会员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editShop")
    public AjaxResult editShop(@RequestBody BillCustomDto billCustom) {
        if (billCustom.getCustomId() == null) {
            throw new BusinessException("not.null");
        }
        EmailUtil.isValidEmail(billCustom.getEmail());
        LoginCustom loginCustom = new LoginCustom();
        loginCustom.setCustomId(billCustom.getCustomId());
        loginCustom.setShopName(billCustom.getShopName());
        loginCustom.setShopRate(billCustom.getShopRate());
        billCustomService.modifyShopName(loginCustom);
        billCustomService.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getEmail, billCustom.getEmail())
                .eq(BillCustom::getCustomId, billCustom.getCustomId()));
        return AjaxResult.success();
    }
}
