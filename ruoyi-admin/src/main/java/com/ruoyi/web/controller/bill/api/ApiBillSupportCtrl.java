package com.ruoyi.web.controller.bill.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.bill.domain.BillSupport;
import com.ruoyi.bill.domain.BillSupportGroup;
import com.ruoyi.bill.domain.condition.BillSupportCondition;
import com.ruoyi.bill.domain.condition.BillSupportGroupCondition;
import com.ruoyi.bill.service.IBillCustomService;
import com.ruoyi.bill.service.IBillSupportGroupService;
import com.ruoyi.bill.service.IBillSupportService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客服idController
 *
 * <AUTHOR>
 * @date 2024-11-10
 */
@RestController
@RequestMapping("/api/bill/support")
public class ApiBillSupportCtrl extends BaseController {
    @Autowired
    private IBillSupportService billSupportService;
    @Autowired
    private IBillSupportGroupService billSupportGroupService;

    @Autowired
    private IBillCustomService billCustomService;

    /**
     * 查询客服id列表
     */
    @GetMapping("/list")
    public AjaxResult list(BillSupportCondition condition) {
        condition.setOrderDesc("support_id");
        Long customId;
        try {
            customId = SecurityUtils.getCustomId();
        } catch (Exception e) {
            BillSupportGroupCondition paramD = new BillSupportGroupCondition();
            paramD.setDefaultGroup("1");
            List<BillSupportGroup> list = billSupportGroupService.selectListByCondition(paramD);
            if (CollectionUtils.isNotEmpty(list)) {
                List<Long> collect = list.stream().map(BillSupportGroup::getSupportGroupId)
                        .collect(Collectors.toList());
                List<BillSupport> list1 = billSupportService.list(new LambdaQueryWrapper<>(BillSupport.class)
                        .in(BillSupport::getSupportGroupId, collect)
                );

                return AjaxResult.success(list1);
            }
            return AjaxResult.success(new ArrayList<>());
        }
        BillCustom custom = billCustomService.getById(customId);
        Long serviceTeamId = custom.getServiceTeamId();
        condition.setSupportGroupId(serviceTeamId);
        List<BillSupport> list = billSupportService.selectListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 查询客服id列表
     */
    @GetMapping("/groups")
    public AjaxResult groups() {
        List<BillSupportGroup> list = billSupportGroupService.selectListByCondition(new BillSupportGroupCondition());
        return AjaxResult.success(list);
    }

}
