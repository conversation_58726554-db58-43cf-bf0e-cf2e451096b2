package com.ruoyi.web.controller.bill.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bill.constant.BillCustomConstant;
import com.ruoyi.bill.constant.OrderTypeEnum;
import com.ruoyi.bill.domain.*;
import com.ruoyi.bill.domain.condition.BillGoodsOrderInfoCondition;
import com.ruoyi.bill.domain.enums.BalanceChangeTypeEnum;
import com.ruoyi.bill.domain.vo.GoodsOrderVO;
import com.ruoyi.bill.domain.vo.TodayCommission;
import com.ruoyi.bill.service.*;
import com.ruoyi.bill.util.FlowNoUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedissonUtil;
import com.ruoyi.common.exception.BusinessErrorCode;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.bill.domain.enums.BalanceChangeTypeEnum.*;
import static com.ruoyi.common.exception.BusinessErrorCode.*;

/**
 * @description:
 * @author：美式续命
 * @date: 2024/6/30
 * @Copyright：
 */

@Api(tags = "h5-订单")
@RestController
@RequestMapping("/api/bill/goodsOrder")
public class GoodsOrderCtrl {

    private static final Logger log = LoggerFactory.getLogger(GoodsOrderCtrl.class);

    @Autowired
    private IBillGoodsOrderInfoService billGoodsOrderInfoService;

    @Autowired
    private IBillGoodsInfoService billGoodsInfoService;

    @Autowired
    private IBillVipService billVipService;

    @Autowired
    private IBillMemberInjectionInfoService billMemberInjectionInfoService;


    @Autowired
    private IBillGoldEggInfoService billGoldEggInfoService;

    @Autowired
    private RedissonUtil redissonUtil;

    @Autowired
    private IBillCustomService billCustomService;

    @Autowired
    private IBillCustomAmountService billCustomAmountService;

    @Autowired
    private IBillCustomGuardService billCustomGuardService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IBillFlowService billFlowService;

    @Autowired
    private IBillBonusHistoryService billBonusHistoryService;

    @Autowired
    private IBillAutoRewardService billAutoRewardService;


    @ApiOperation(value = "商品订单-提交订单", notes = "商品订单-提交订单")
    @RequestMapping(value = "/submit", method = {RequestMethod.GET})
    @Transactional
    public AjaxResult submit() {

        // 校验存在未完成订单
        Long customId = SecurityUtils.getCustomId();
        long existedOrderCount = getExistedOrderCount(customId);
        // 存在未完成订单返回未完成标
        if (existedOrderCount > 0) {
            GoodsOrderVO data = new GoodsOrderVO();
            data.setExistedOrder(true);
            return AjaxResult.success(data);
        }

        // 管理员对这个用户重置了多少次任务最终也不能越过这个 当日交易次数
        BillCustom custom = billCustomService.getById(customId);
        BillVip billVip = billVipService.getVipListByLevel(custom.getLevel());

        // 当日交易次数限制
        checkTradeDayOfLimit(customId, billVip);

        // 获取余额保证
        BillCustomGuard customGuard = billCustomGuardService.getById(customId);
        // 加锁处理业务逻辑
        String key = CacheConstants.getMemberAccountKey();
        GoodsOrderVO goodsOrderVO = redissonUtil.lockProcessBusiness(key, (param) -> {
            long timeMillis = System.currentTimeMillis();
            String orderNo = "UB" + timeMillis;
            List<BillFlow> flows = new ArrayList<>();
            BigDecimal billAutoRewardAmount = BigDecimal.ZERO;

            // 查询账户信息
            BillCustomAmount account = billCustomAmountService.getById(customId);

            // 余额护额
            BigDecimal balance = account.getBalance();
            BigDecimal oldBalance = account.getBalance();
            // 用户佣金
            BigDecimal commission = account.getCommission();
            // 当前订单数量
            Long orderCount = getOrderNumber(account.getCurrentOrderCount());
            // 做单总数
            Long totalDoOrder = getOrderNumber(account.getTotalOrderCount());
            // 当前做单数=当前订单数量+1
            Long currentDoOrder = orderCount + 1;
            totalDoOrder = totalDoOrder + 1;

            // 校验是否超过做单数
            checkTodayOfOrderNum(currentDoOrder, billVip);

            // 查询打针订单
            List<BillMemberInjectionInfo> injectionInfo = queryBillMemberInjectionInfo(customId, currentDoOrder);

            // 做单最低余额判断
            accountBalanceVerify(customGuard, billVip, currentDoOrder, injectionInfo, balance);

            // 处理自动赠金
            BillAutoReward billAutoReward = null;
            try {
                billAutoReward = billAutoRewardService.handlerAutoReward(customId, currentDoOrder, 0L);
            } catch (Exception e) {
                log.error("Error processing bill auto reward", e);
            }

            // 发钱的流水
            if (billAutoReward != null) {
                balance = balance.add(billAutoReward.getRewardAmount());
                BillFlow bonusFlow = buildFlow(balance, billAutoReward.getRewardAmount(), account.getCustomId(),
                        account.getUsername(), oldBalance, orderNo, timeMillis, auto_reward);
                flows.add(bonusFlow);
                oldBalance = oldBalance.add(billAutoReward.getRewardAmount());
            }

            // 查询商品信息
            List<BillGoodsInfo> goodsInfo = queryGoodsInfo(injectionInfo, billVip, custom, balance);

            // 判断订单类型
            Long orderType = judgmentOrderType(injectionInfo);

            // 查询金蛋信息
            List<BillGoldEggInfo> goldEggInfo = queryGoldEgg(orderType, injectionInfo, currentDoOrder);

            // 计算购买数量
            Map<Long, Long> goodsNumber = calculateBuyNumber(injectionInfo, goodsInfo, balance, billVip);

            // 订单总金额
            BigDecimal orderTotalPrice = BigDecimal.ZERO;
            for (BillGoodsInfo info : goodsInfo) {
                orderTotalPrice = info.getGoodsPrice().multiply(new BigDecimal(String.valueOf(goodsNumber.get(info.getId()))));
            }

            // 计算是否符合条件
            boolean checkAndSetOrderStatus = validateAndUpdateOrderStatus(balance, orderTotalPrice, injectionInfo, goldEggInfo);

            List<BillGoodsOrderInfo> billGoodsOrderInfoList;
            // 不符合条件记录一笔订单,返回售罄标签
            if (checkAndSetOrderStatus) {
                if (Objects.equals(billVip.getSoldOut(), 1)) {
                    billGoodsOrderInfoList = buildBaseGoodsOrderInfo(goodsInfo, goodsNumber, balance, custom, 3L, orderType, orderNo);
                    billGoodsOrderInfoService.saveBatch(billGoodsOrderInfoList);
                    BillBonusHistory billBonusHistory = handlerBonus(currentDoOrder, customId);

                    if (billBonusHistory != null) {
                        balance = balance.subtract(billBonusHistory.getAmount());
                        BillFlow bonusFlow = buildFlow(balance, billBonusHistory.getAmount(), account.getCustomId(),
                                account.getUsername(), oldBalance, orderNo, timeMillis, TYPE_EXPERIENCE_BONUS_OUT);
                        billFlowService.save(bonusFlow);
                    }
                    // 更新做单数量
                    billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                            .set(BillCustomAmount::getCurrentOrderCount, currentDoOrder)
                            .set(account.getBalance().compareTo(balance) != 0, BillCustomAmount::getBalance, balance)
                            .set(BillCustomAmount::getTotalOrderCount, totalDoOrder + 1)
                            .eq(BillCustomAmount::getCustomId, customId)
                    );
                }
                GoodsOrderVO vo = new GoodsOrderVO();
                vo.setSoldOut(true);
                return vo;
            }

            // 处理打针订单
            if (CollectionUtils.isNotEmpty(injectionInfo)) {
                List<Long> stringList = injectionInfo.stream()
                        .map(BillMemberInjectionInfo::getId)
                        .collect(Collectors.toList());

                billMemberInjectionInfoService.update(new LambdaUpdateWrapper<>(BillMemberInjectionInfo.class)
                        .set(BillMemberInjectionInfo::getStatus, 1)
                        .set(BillMemberInjectionInfo::getOrderNo, orderNo)
                        .in(BillMemberInjectionInfo::getId, stringList));
            }


            // 金蛋不为空做状态更新&奖励发给用户
            if (CollectionUtils.isNotEmpty(goldEggInfo) && !Objects.equals(orderType, OrderTypeEnum.ORDER_TYPE_GOLD_EGG.getValue())) {
                //更新金蛋状态
                updateGoldEggStatus(goldEggInfo, orderNo);
                BigDecimal giftAmount = goldEggInfo.get(0).getGiftAmount();
                balance = balance.add(giftAmount);
                commission = commission.add(giftAmount);
                // 新加流水
                BillFlow goldEggFlow = buildFlow(balance, giftAmount, account.getCustomId(), account.getUsername(), oldBalance, orderNo, timeMillis, TYPE_GOLD_EGG);
                flows.add(goldEggFlow);
                oldBalance = oldBalance.add(giftAmount);
            }


            // 扣减余额
            balance = balance.subtract(orderTotalPrice);

            // 计算佣金
            BigDecimal orderTotalCommission = calculateOrderCommission(orderType, billVip, orderTotalPrice, injectionInfo, custom, currentDoOrder);
            // 计算用户总冻结金额
            BigDecimal frozenAmount = BigDecimal.ZERO;
            if (balance.compareTo(BigDecimal.ZERO) < 0) {
                // 冻结金额=打针金额+佣金
                frozenAmount = orderTotalPrice.add(orderTotalCommission);
            }

            // 处理自动赠金
            if (billAutoReward != null) {
                goldEggInfo = buildBillGoldEggInfo(goldEggInfo, billAutoReward);
            }

            // 如果是联单的情况下，需要重新计算用户的做单数  当前做单数=currentDoOrder+打针商品-1
            if (CollectionUtils.isNotEmpty(injectionInfo)) {
                currentDoOrder = currentDoOrder + injectionInfo.size() - 1;
                totalDoOrder = totalDoOrder + injectionInfo.size() - 1;
            }

            // 更新用户账单
            billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .set(BillCustomAmount::getBalance, balance)
                    .set(BillCustomAmount::getFrozenAmount, frozenAmount)
                    .set(BillCustomAmount::getCommission, commission)
                    .set(BillCustomAmount::getCurrentOrderCount, currentDoOrder)
                    .set(BillCustomAmount::getTotalOrderCount, totalDoOrder)
                    .eq(BillCustomAmount::getCustomId, customId)
            );

            // 构建订单信息
            billGoodsOrderInfoList = buildOrderInfo(goodsInfo, goodsNumber, balance, oldBalance, custom, orderType, injectionInfo, orderNo, billVip);

            // 如果金蛋信息不为空，需要补充一下金蛋id
            if (CollectionUtils.isNotEmpty(goldEggInfo)) {
                for (BillGoodsOrderInfo orderInfo : billGoodsOrderInfoList) {
                    orderInfo.setGoldOrderId(goldEggInfo.get(0).getId());
                }
            }

            // 保存订单信息
            billGoodsOrderInfoService.saveBatch(billGoodsOrderInfoList);
            // 新加购买商品流水
            account.setBalance(account.getBalance().add(billAutoRewardAmount));
            BillFlow billFlow = buildFlow(balance, orderTotalPrice, account.getCustomId(), account.getUsername(), oldBalance, orderNo, timeMillis, TYPE_PURCHASE);
            flows.add(billFlow);
            billFlowService.insertBatch(flows);

            return convertVO(billGoodsOrderInfoList, goodsInfo, goldEggInfo);
        }, null);

        if (goodsOrderVO.getSoldOut()) {
            return AjaxResult.error(MessageUtils.message(ORDER_IS_SOLD_OUT.getMessage()));
        }
        return AjaxResult.success(goodsOrderVO);
    }


    @ApiOperation(value = "商品订单-submitOrder提交评论", notes = "商品订单-submitOrder提交评论")
    @RequestMapping(value = "/doOrder", method = {RequestMethod.POST})
    @Transactional
    public AjaxResult doOrder(@RequestBody BillGoodsOrderInfo billGoodsOrderInfo) {
        if (StringUtils.isBlank(billGoodsOrderInfo.getOrderNo())) {
            return AjaxResult.error(MessageUtils.message(order_no_cannot_be_empty.getMessage()));
        }
        Long customId = SecurityUtils.getCustomId();
        List<BillGoodsOrderInfo> goodsOrderInfoList = billGoodsOrderInfoService.list(new LambdaUpdateWrapper<>(
                BillGoodsOrderInfo.class)
                .eq(BillGoodsOrderInfo::getOrderNo, billGoodsOrderInfo.getOrderNo())
                .eq(BillGoodsOrderInfo::getCustomId, customId)
        );
        if (CollectionUtils.isEmpty(goodsOrderInfoList)) {
            return AjaxResult.error(MessageUtils.message(the_order_does_not_exist.getMessage()));
        }
        List<BillGoodsOrderInfo> collect = goodsOrderInfoList.stream()
                .filter(var -> var.getOrderStatus() != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return AjaxResult.error(MessageUtils.message(the_order_has_been_processed.getMessage()));
        }
        // 加锁处理
        BillCustom custom = billCustomService.getById(customId);
        String key = CacheConstants.getMemberAccountKey();
        GoodsOrderVO goodsOrderVO = redissonUtil.lockProcessBusiness(key, (var) -> {
            long timeMillis = System.currentTimeMillis();
            List<BillFlow> billFlows = new ArrayList<>();
            BillCustomAmount account = billCustomAmountService.getById(customId);
            BigDecimal balance = account.getBalance();
            BigDecimal oldBalance = account.getBalance();


            if (goodsOrderInfoList.get(0).getOrderType() != 0) {
                // 非普通订单校验
                verifyBalance(balance, goodsOrderInfoList);
            }

            BigDecimal totalExchangeAmount = BigDecimal.ZERO;
            BigDecimal totalCommission = BigDecimal.ZERO;
            for (BillGoodsOrderInfo orderInfo : goodsOrderInfoList) {
                BigDecimal exchangeAmount = orderInfo.getExchangeAmount();
                totalExchangeAmount = totalExchangeAmount.add(exchangeAmount);
                totalCommission = totalCommission.add(orderInfo.getCommission());
            }
            // 计算用户余额===>公式：用户余额=冻结金额+佣金
            balance = balance.add(totalExchangeAmount).add(totalCommission);

            // 佣金
            BigDecimal commission = account.getCommission();

            // 处理订单佣金
            commission = commission.add(totalCommission);

            // 处理订单佣金流水
            String orderNo = goodsOrderInfoList.get(0).getOrderNo();
            BillFlow goodsRebateFlow = buildFlow(balance, totalCommission, account.getCustomId(), account.getUsername(),
                    oldBalance, orderNo, timeMillis, TYPE_GOODS_REBATE);
            billFlows.add(goodsRebateFlow);

            // 处理完订单的流水再把钱加到old上
            oldBalance = oldBalance.add(totalExchangeAmount).add(totalCommission);
            // 查询金蛋 && 处理金蛋相关
            List<BillGoldEggInfo> goldEggInfos = queryGoldEgg(account, goodsOrderInfoList);
            if (CollectionUtils.isNotEmpty(goldEggInfos)) {
                BillGoldEggInfo goldEggInfo = goldEggInfos.get(0);
                balance = balance.add(goldEggInfo.getGiftAmount());
                commission = commission.add(goldEggInfo.getGiftAmount());
                BillFlow goldEggFlow = buildFlow(balance, goldEggInfo.getGiftAmount(), account.getCustomId(), account.getUsername(), oldBalance, orderNo, timeMillis, TYPE_GOLD_EGG);
                billFlows.add(goldEggFlow);
                oldBalance = oldBalance.add(goldEggInfo.getGiftAmount());
            }

            // 回收用户体验金
            BillBonusHistory billBonusHistory = handlerBonus(account.getCurrentOrderCount(), customId);
            if (billBonusHistory != null) {
                balance = balance.subtract(billBonusHistory.getAmount());
                BillFlow bonusFlow = buildFlow(balance, billBonusHistory.getAmount(), account.getCustomId(), account.getUsername(), oldBalance, orderNo, timeMillis, TYPE_EXPERIENCE_BONUS_OUT);
                billFlows.add(bonusFlow);
                oldBalance = oldBalance.subtract(billBonusHistory.getAmount());
            }

            // 处理自动赠金
            BillAutoReward billAutoReward = null;
            try {
                billAutoReward = billAutoRewardService.handlerAutoReward(customId, account.getCurrentOrderCount(), 1L);
            } catch (Exception e) {
                log.error("Error processing bill bonus", e);
            }
            if (billAutoReward != null) {
                balance = balance.add(billAutoReward.getRewardAmount());
                BillFlow bonusFlow = buildFlow(balance, billAutoReward.getRewardAmount(), account.getCustomId(),account.getUsername(),oldBalance, orderNo, timeMillis, auto_reward);
                billFlows.add(bonusFlow);
                goldEggInfos = buildBillGoldEggInfo(goldEggInfos, billAutoReward);
            }

            // 更新账户金额 && 生成商品返利流水
            updateAccount(balance, commission);

            // 处理上级佣金问题
            BillFlow billFlow = handlerParentCommission(custom, totalCommission, orderNo);
            Optional.ofNullable(billFlow).ifPresent(billFlows::add);

            // 更新打针信息  订单状态 商品评论数
            updateInjectionInfoAndGoldEggAndGoodsOrderInfoAndGoodsComment(goodsOrderInfoList);

            // 批量插入流水
            billFlowService.insertBatch(billFlows);
            return convertVO(goodsOrderInfoList, null, goldEggInfos);
        }, null);
        return AjaxResult.success(goodsOrderVO);
    }

    @NotNull
    private static List<BillGoldEggInfo> buildBillGoldEggInfo(List<BillGoldEggInfo> goldEggInfo, BillAutoReward billAutoReward) {
        if (CollectionUtils.isNotEmpty(goldEggInfo)) {
            BillGoldEggInfo goldEggInfo2 = new BillGoldEggInfo();
            goldEggInfo2.setId(billAutoReward.getAutoRewardId());
            goldEggInfo2.setGiftType(0L);
            goldEggInfo2.setGiftAmount(billAutoReward.getRewardAmount());
            goldEggInfo2.setModeArrival(billAutoReward.getModeArrival());
            goldEggInfo2.setGiftStatus(0L);
            return Arrays.asList(goldEggInfo2);
        }
        return goldEggInfo;
    }

    @PostMapping("/orderList")
    @ApiOperation(value = "商品订单-订单列表", notes = "商品订单-订单列表")
    public AjaxResult orderList(@RequestBody BillGoodsOrderInfoCondition billGoodsOrderInfo) {
        Long customId = SecurityUtils.getCustomId();
        Page<BillGoodsOrderInfo> page = new Page<>(billGoodsOrderInfo.getPageNum(), billGoodsOrderInfo.getPageSize());
        billGoodsOrderInfoService.page(page, new LambdaUpdateWrapper<>(BillGoodsOrderInfo.class)
                .eq(Objects.nonNull(billGoodsOrderInfo.getOrderStatus()), BillGoodsOrderInfo::getOrderStatus,
                        billGoodsOrderInfo.getOrderStatus())
                .eq(BillGoodsOrderInfo::getCustomId, customId)
                .orderByDesc(BillGoodsOrderInfo::getId)
        );
        return AjaxResult.success(page);
    }

    private static List<BillGoodsOrderInfo> buildOrderInfo(List<BillGoodsInfo> goodsInfo, Map<Long, Long> buyNumber,
                                                           BigDecimal balance, BigDecimal oldBalance, BillCustom custom, Long orderType,
                                                           List<BillMemberInjectionInfo> injectionInfo, String orderNo, BillVip billVip) {
        List<BillGoodsOrderInfo> billGoodsOrderInfos = buildBaseGoodsOrderInfo(goodsInfo, buyNumber, balance, custom, 0L, orderType, orderNo);
        for (BillGoodsOrderInfo orderInfo : billGoodsOrderInfos) {
            BigDecimal frozenAmount = BigDecimal.ZERO;
            if (balance.compareTo(BigDecimal.ZERO) < 0) {
                frozenAmount = oldBalance;
            }
            orderInfo.setFrozenAmount(frozenAmount);
            BigDecimal orderCommission;
            if (orderType == 0) {
                // 普通订单走vip等级的费率计算
                orderCommission = calculateCommission(billVip, orderInfo.getGoodsTotalPrice());
            } else {
                // 打针订单走单独的逻辑计算
                Map<Long, BillMemberInjectionInfo> collect = injectionInfo.stream()
                        .collect(Collectors.toMap(BillMemberInjectionInfo::getGoodsId, Function.identity()));
                BillMemberInjectionInfo injectionInfo1 = collect.get(orderInfo.getGoodsId());
                if (injectionInfo1 != null) {
                    orderCommission = injectionInfo1.getTotalCommission();
                    orderInfo.setInjectionId(injectionInfo1.getId());
                    orderInfo.setCommissionMultiple(injectionInfo1.getCommissionMultiple());
                } else {
                    orderCommission = calculateCommission(billVip, orderInfo.getGoodsTotalPrice());
                }
            }
            orderInfo.setCommission(orderCommission);

        }
        return billGoodsOrderInfos;
    }

    private static List<BillGoodsOrderInfo> buildBaseGoodsOrderInfo(List<BillGoodsInfo> goodsInfoList,
                                                                    Map<Long, Long> buyNumber, BigDecimal balance,
                                                                    BillCustom custom, Long orderStatus, Long orderType, String orderNo) {

        List<BillGoodsOrderInfo> billGoodsOrderInfo = new ArrayList<>();
        BigDecimal tempBalance = balance;
        for (BillGoodsInfo goodsInfo : goodsInfoList) {
            Long goodsNum = buyNumber.get(goodsInfo.getId());
            BigDecimal orderTotalPrice = goodsInfo.getGoodsPrice().multiply(new BigDecimal(String.valueOf(goodsNum)));
            BillGoodsOrderInfo orderInfo = new BillGoodsOrderInfo();
            orderInfo.setCustomId(custom.getCustomId());
            orderInfo.setOrderNo(orderNo);
            orderInfo.setUsername(custom.getUsername());
            orderInfo.setPhoneCode(custom.getPhoneCode());
            orderInfo.setPhone(custom.getPhone());
            orderInfo.setGoodsId(goodsInfo.getId());
            orderInfo.setGoodsNum(goodsNum);
            orderInfo.setGoodsPrice(goodsInfo.getGoodsPrice());
            orderInfo.setGoodsName(goodsInfo.getGoodsName());
            orderInfo.setShopName(goodsInfo.getShopName());
            orderInfo.setGoodsLogo(goodsInfo.getGoodsLogo());
            orderInfo.setBalance(tempBalance);
            orderInfo.setExchangeAmount(orderTotalPrice);
            orderInfo.setOrderStatus(orderStatus);
            orderInfo.setDoOrderLevel(custom.getLevel());
            orderInfo.setContent(goodsInfo.getDescContent());
            orderInfo.setOrderType(orderType);
            orderInfo.setFrozenAmount(BigDecimal.ZERO);
            orderInfo.setGoodsTotalPrice(orderTotalPrice);
            orderInfo.setCommission(BigDecimal.ZERO);
            billGoodsOrderInfo.add(orderInfo);
            tempBalance = tempBalance.subtract(orderTotalPrice);
        }
        return billGoodsOrderInfo;

    }

    private BigDecimal calculateOrderCommission(Long orderType, BillVip billVip, BigDecimal orderTotalPrice,
                                                       List<BillMemberInjectionInfo> injectionInfo, BillCustom custom, Long currentDoOrder) {
        BigDecimal orderCommission;
        if (orderType == 0) {
            // 普通订单走vip等级的费率计算
            // 如果是普通订单并且启用指定佣金模式
            String enableSpecified = custom.queryExtra(BillCustomConstant.enableSpecifiedCommission);
            if (StringUtils.equals(enableSpecified, "1")) {
                // 获取当前已累计的佣金总额
                String totalCommissionStr = custom.queryExtra(BillCustomConstant.specifiedTotalCommission);
                BigDecimal currentTotal = StringUtils.isBlank(totalCommissionStr) ? BigDecimal.ZERO : new BigDecimal(totalCommissionStr);

                // 目标总佣金范围 90-100
                BigDecimal targetMin = new BigDecimal("90");
                BigDecimal targetMax = new BigDecimal("100");

                BigDecimal thisOrderCommission;

                // 获取或设置目标总佣金
                String targetTotalStr = custom.queryExtra("specifiedTargetTotal");
                BigDecimal targetTotal;
                Long totalOrderCount = billVip.getOrderCount();

                if (StringUtils.isBlank(targetTotalStr)) {
                    // 第一单，设置目标总佣金
                    targetTotal = RandomUtil.randomBigDecimal(targetMin, targetMax);
                    billCustomService.updateExtra(custom.getCustomId(), "specifiedTargetTotal", targetTotal.toString());
                } else {
                    targetTotal = new BigDecimal(targetTotalStr);
                }

                if (currentDoOrder >= totalOrderCount) {
                    // 最后一单，直接给到目标总额
                    thisOrderCommission = targetTotal.subtract(currentTotal);
                } else {
                    // 非最后一单，智能分配佣金
                    long remainingOrders = totalOrderCount - currentDoOrder;
                    BigDecimal remainingCommission = targetTotal.subtract(currentTotal);

                    // 计算平均每单应分配的佣金
                    BigDecimal averageCommission = remainingCommission.divide(new BigDecimal(remainingOrders), 2, RoundingMode.HALF_UP);

                    // 计算单笔佣金的合理范围
                    BigDecimal singleMin = targetMin.divide(new BigDecimal(totalOrderCount), 2, RoundingMode.HALF_UP);
                    BigDecimal singleMax = targetMax.divide(new BigDecimal(totalOrderCount), 2, RoundingMode.HALF_UP);

                    // 在平均值基础上增加随机性 (±40%)，但不超出合理范围
                    BigDecimal variation = averageCommission.multiply(new BigDecimal("0.4"));
                    BigDecimal minCommission = averageCommission.subtract(variation);
                    BigDecimal maxCommission = averageCommission.add(variation);

                    // 确保在合理的单笔范围内
                    if (minCommission.compareTo(singleMin) < 0) {
                        minCommission = singleMin;
                    }
                    if (maxCommission.compareTo(singleMax.multiply(new BigDecimal("1.5"))) > 0) {
                        maxCommission = singleMax.multiply(new BigDecimal("1.5"));
                    }

                    // 确保不会导致后续单数无法合理分配
                    BigDecimal maxSafeCommission = remainingCommission.subtract(singleMin.multiply(new BigDecimal(remainingOrders - 1)));
                    if (maxCommission.compareTo(maxSafeCommission) > 0) {
                        maxCommission = maxSafeCommission;
                    }

                    thisOrderCommission = RandomUtil.randomBigDecimal(minCommission, maxCommission);
                }
                // 更新累计佣金总额
                BigDecimal newTotal = currentTotal.add(thisOrderCommission);
                billCustomService.updateExtra(custom.getCustomId(), BillCustomConstant.specifiedTotalCommission, newTotal.toString());
                orderCommission = thisOrderCommission;
            } else {
                orderCommission = calculateCommission(billVip, orderTotalPrice);
            }
        } else {
            // 打针订单走单独的逻辑计算
            orderCommission = BigDecimal.ZERO;
            for (BillMemberInjectionInfo billMemberInjectionInfo : injectionInfo) {
                orderCommission = orderCommission.add(billMemberInjectionInfo.getTotalCommission());
            }
        }
        return orderCommission;
    }

    private boolean validateAndUpdateOrderStatus(BigDecimal userBalance, BigDecimal totalOrderPrice, List<BillMemberInjectionInfo> injectionInfo, List<BillGoldEggInfo> goldEggInfo) {
        boolean notEnoughBalance = userBalance.compareTo(totalOrderPrice) < 0;
        if (CollectionUtils.isEmpty(goldEggInfo) && CollectionUtils.isEmpty(injectionInfo) && notEnoughBalance) {
            String key = getUserDoOrderKey();
            handlerCache(key);
            return true;
        }
        String userLotteryDoOrderKey = getUserLotteryDoOrderKey();
        handlerCache(userLotteryDoOrderKey);
        return false;
    }

    private void handlerCache(String key) {
        if (redisCache.hasKey(key)) {
            int value = Integer.parseInt(redisCache.getCacheObject(key).toString());
            Integer newValue = value + 1;
            redisCache.setCacheObject(key, newValue, 86400, TimeUnit.SECONDS);
        } else {
            redisCache.setCacheObject(key, 1, 86400, TimeUnit.SECONDS);
        }
    }

    @NotNull
    private static String getUserDoOrderKey() {
        return CacheConstants.USER_DO_ORDER + SecurityUtils.getCustomId();
    }

    @NotNull
    private static String getUserLotteryDoOrderKey() {
        return CacheConstants.LOTTERY_USER_DO_ORDER + SecurityUtils.getCustomId();
    }


    /***
     *  查询金蛋信息
     * @return
     */
    private List<BillGoldEggInfo> queryGoldEgg(Long orderType, List<BillMemberInjectionInfo> injectionInfo, Long currentDoOrder) {
        List<BillGoldEggInfo> goldOrder;
        if (Objects.equals(orderType, OrderTypeEnum.ORDER_TYPE_GOLD_EGG.getValue())) {
            List<Long> goodsIdList = injectionInfo.stream()
                    .map(BillMemberInjectionInfo::getGoodsId)
                    .collect(Collectors.toList());
            goldOrder = billGoldEggInfoService.listByIds(goodsIdList);
        } else {
            // 处理金蛋
            goldOrder = billGoldEggInfoService.list(new LambdaUpdateWrapper<>(
                    BillGoldEggInfo.class)
                    .eq(BillGoldEggInfo::getCustomId, SecurityUtils.getCustomId())
                    .eq(BillGoldEggInfo::getGiftType, 0L)
                    .eq(BillGoldEggInfo::getGiftOrder, currentDoOrder)
                    .eq(BillGoldEggInfo::getModeArrival, 0L)
                    .eq(BillGoldEggInfo::getGiftStatus, 0L)
                    .last("limit 1")
            );
        }
        return goldOrder;

    }

    private void updateGoldEggStatus(List<BillGoldEggInfo> goldOrder, String orderNo) {
        if (Objects.nonNull(goldOrder)) {
            List<Long> collect = goldOrder.stream().map(BillGoldEggInfo::getId).collect(Collectors.toList());
            billGoldEggInfoService.update(new LambdaUpdateWrapper<>(BillGoldEggInfo.class)
                    .set(BillGoldEggInfo::getGiftStatus, 1L)
                    .set(BillGoldEggInfo::getUesTime, System.currentTimeMillis())
                    .set(BillGoldEggInfo::getOrderNo, orderNo)
                    .in(BillGoldEggInfo::getId, collect)
            );

        }
    }

    private Long judgmentOrderType(List<BillMemberInjectionInfo> injectionInfo) {
        return CollectionUtils.isNotEmpty(injectionInfo) ? injectionInfo.get(0).getOrderType() : 0L;
    }

    private List<BillGoodsInfo> queryGoodsInfo(List<BillMemberInjectionInfo> injectionInfo, BillVip billVip, BillCustom custom, BigDecimal balance) {
        List<BillGoodsInfo> goodsInfo;
        if (CollectionUtils.isNotEmpty(injectionInfo)) {
            List<Long> collect = injectionInfo.stream()
                    .map(BillMemberInjectionInfo::getGoodsId)
                    .collect(Collectors.toList());
            goodsInfo = billGoodsInfoService.listByIds(collect);
        } else {
            BigDecimal leftValue;
            BigDecimal rightValue;
            if (
                    (Objects.nonNull(custom.getRangeMin()) && Objects.nonNull(custom.getRangeMax()))
                            && (custom.getRangeMin().compareTo(BigDecimal.ZERO) != 0 && custom.getRangeMax().compareTo(BigDecimal.ZERO) != 0)
            ) {
                leftValue = custom.getRangeMin();
                rightValue = custom.getRangeMax();
            } else {
                leftValue = billVip.getRangeMin();
                rightValue = billVip.getRangeMax();
            }
            /**这边不要除以100 因为商品的价格是按照分存储的**/
            BigDecimal leftPrice = balance.multiply(leftValue).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            BigDecimal rightPrice = balance.multiply(rightValue).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            BillGoodsInfo randQueryGood = billGoodsInfoService.randQueryGood(leftPrice, rightPrice);
            if (Objects.isNull(randQueryGood)) {
                randQueryGood = billGoodsInfoService.getOne(new LambdaUpdateWrapper<>(BillGoodsInfo.class)
                        .orderByDesc(BillGoodsInfo::getId)
                        .last("limit 1")
                );
            }
            goodsInfo = Arrays.asList(randQueryGood);
        }
        return goodsInfo;
    }

    private Map<Long, Long> calculateBuyNumber(List<BillMemberInjectionInfo> injectionInfo, List<BillGoodsInfo> goodsInfo, BigDecimal balance, BillVip billVip) {
        Map<Long, Long> goodsNumber = new HashMap<>();
        if (CollectionUtils.isNotEmpty(injectionInfo)) {
            for (BillMemberInjectionInfo info : injectionInfo) {
                goodsNumber.put(info.getGoodsId(), info.getGoodsNum());
            }
            return goodsNumber;
        } else {
            //计算总价
            for (BillGoodsInfo billGoodsInfo : goodsInfo) {
                if (isAllowedDo(billVip)) {
                    goodsNumber.put(billGoodsInfo.getId(), calculateSaleOutNumber(billGoodsInfo, balance));
                } else if (isDoOrder(billVip)) {
                    goodsNumber.put(billGoodsInfo.getId(), calculateNum2(billGoodsInfo, balance));
                } else {
                    goodsNumber.put(billGoodsInfo.getId(), calculateNum(billGoodsInfo, balance));
                }

            }
        }
        return goodsNumber;
    }

    private boolean isDoOrder(BillVip billVip) {
        String key = getUserDoOrderKey();
        long soldOut = 0L;
        if (redisCache.hasKey(key)) {
            soldOut = Long.parseLong(redisCache.getCacheObject(key).toString());
        }
        return soldOut >= billVip.getTipSoldOut();
    }

    private boolean isAllowedDo(BillVip billVip) {
        String userLotteryDoOrderKey = getUserLotteryDoOrderKey();
        Long orderCount = billVip.getOrderCount();
        Long tipSoldOut = billVip.getTipSoldOut();
        // 当日允许做单数=可做单数-售罄提示数
        long allowedDoOrder = orderCount - tipSoldOut;
        return redisCache.hasKey(userLotteryDoOrderKey) && Long.parseLong(redisCache.getCacheObject(userLotteryDoOrderKey).toString()) >= allowedDoOrder;
    }


    private void accountBalanceVerify(BillCustomGuard customGuard, BillVip billVip, Long currentDoOrder,
                                      List<BillMemberInjectionInfo> injectionInfo, BigDecimal balance) {
        BillCustomGuard.GuardJson guardJson = queryGuard(customGuard, billVip, currentDoOrder);
        if (Objects.nonNull(guardJson) && CollectionUtils.isNotEmpty(injectionInfo) && (balance.compareTo(guardJson.getBalance()) < 0)) {
            throw new BusinessException(BALANCE_NOT_ENOUGH);
        }
    }

    private static Long getOrderNumber(Long account) {
        return Objects.isNull(account) ? 0L : account;
    }

    private void checkTodayOfOrderNum(Long currentDoOrder, BillVip billVip) {
        if (StringUtils.equalsIgnoreCase("0", billVip.getRepeatOrder())) {
            Long orderCount = billVip.getOrderCount();
            if (currentDoOrder > orderCount) {
                throw new BusinessException(NUMBER_OF_ORDERS_TODAY_LIMIT);
            }
        }
    }

    private void checkTradeDayOfLimit(Long customId, BillVip billVip) {
        long todayDoOrderCount = getTodayDoOrderCount(customId);
        if (todayDoOrderCount >= billVip.getTradeDayLimit()) {
            throw new BusinessException(TRADE_ORDER_DO_ORDER_LIMIT);
        }
    }

    private long getExistedOrderCount(Long customId) {
        return queryExistedOrderCount(new LambdaUpdateWrapper<>(BillGoodsOrderInfo.class)
                .eq(BillGoodsOrderInfo::getCustomId, customId)
                .eq(BillGoodsOrderInfo::getOrderStatus, 0));
    }

    private long getTodayDoOrderCount(Long customId) {
        return queryExistedOrderCount(new LambdaUpdateWrapper<>(BillGoodsOrderInfo.class)
                .eq(BillGoodsOrderInfo::getCustomId, customId)
                .eq(BillGoodsOrderInfo::getOrderStatus, 1));
    }

    private long queryExistedOrderCount(LambdaUpdateWrapper<BillGoodsOrderInfo> customId) {
        return billGoodsOrderInfoService.count(customId
        );
    }


    private List<BillGoldEggInfo> queryGoldEgg(BillCustomAmount account, List<BillGoodsOrderInfo> goodsOrderInfo) {
        /**处理金蛋**/
        BillGoldEggInfo goldOrder = billGoldEggInfoService.getOne(new LambdaUpdateWrapper<>(
                BillGoldEggInfo.class)
                .eq(BillGoldEggInfo::getCustomId, account.getCustomId())
                .eq(BillGoldEggInfo::getGiftType, 0L)
                .eq(BillGoldEggInfo::getGiftOrder, account.getCurrentOrderCount())
                .eq(BillGoldEggInfo::getModeArrival, 1L)
                .eq(BillGoldEggInfo::getGiftStatus, 0L)
        );
        if (Objects.nonNull(goldOrder)) {
            goodsOrderInfo.forEach(var -> {
                var.setGoldOrderId(goldOrder.getId());
            });
            billGoldEggInfoService.update(new LambdaUpdateWrapper<>(BillGoldEggInfo.class)
                    .set(BillGoldEggInfo::getGiftStatus, 1L)
                    .set(BillGoldEggInfo::getUesTime, System.currentTimeMillis())
                    .eq(BillGoldEggInfo::getId, goldOrder.getId())
            );
            return Arrays.asList(goldOrder);
        }
        return null;
    }

    private void verifyBalance(BigDecimal balance, List<BillGoodsOrderInfo> goodsOrderInfoList) {
        if (balance.compareTo(BigDecimal.ZERO) < 0) {
            Long orderType = goodsOrderInfoList.stream()
                    .map(BillGoodsOrderInfo::getOrderType)
                    .findFirst()
                    .orElse(null);
            Long multiple = goodsOrderInfoList.get(0)
                    .getCommissionMultiple();

            List<Long> goldOrderIdList = goodsOrderInfoList.stream()
                    .map(BillGoodsOrderInfo::getGoldOrderId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            BusinessErrorCode errorCode;
            if (Objects.equals(orderType, 1L)) {
                errorCode = advanced_order_error_msg;
            } else if (Objects.equals(orderType, 2L)) {
                errorCode = luxury_order_error_msg;
            } else if (Objects.equals(orderType, 3L) || CollectionUtils.isNotEmpty(goldOrderIdList)) {
                errorCode = gold_egg_error_msg;
            } else {
                errorCode = common_order_error_msg;
            }
            String msg = MessageUtils.message(errorCode.getMessage(), multiple);
            throw new ServiceException(msg.replace("{0}", multiple.toString()), errorCode.getCode());
        }
    }

    private void updateAccount(BigDecimal balance,
                               BigDecimal commission) {
        // 更新金额
        billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                .set(BillCustomAmount::getBalance, balance)
                .set(BillCustomAmount::getFrozenAmount, BigDecimal.ZERO)
                .set(BillCustomAmount::getCommission, commission)
                .eq(BillCustomAmount::getCustomId, SecurityUtils.getCustomId())
        );
    }

    private void updateInjectionInfoAndGoldEggAndGoodsOrderInfoAndGoodsComment(List<BillGoodsOrderInfo> goodsOrderInfoList) {
        long now = System.currentTimeMillis();
        List<Long> injectionIdList = goodsOrderInfoList.stream()
                .map(BillGoodsOrderInfo::getInjectionId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 处理打针订单
        if (CollectionUtils.isNotEmpty(injectionIdList)) {
            billMemberInjectionInfoService.update(new LambdaUpdateWrapper<>(BillMemberInjectionInfo.class)
                    .set(BillMemberInjectionInfo::getStatus, 2)
                    .set(BillMemberInjectionInfo::getOrderFinishTime, now)
                    .in(BillMemberInjectionInfo::getId, injectionIdList));
        }

        // 处理金蛋订单
        List<Long> goldOrderIdList = goodsOrderInfoList.stream()
                .map(BillGoodsOrderInfo::getGoldOrderId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goldOrderIdList)) {
            billGoldEggInfoService.update(new LambdaUpdateWrapper<>(BillGoldEggInfo.class)
                    .set(BillGoldEggInfo::getGiftStatus, 1L)
                    .set(BillGoldEggInfo::getUesTime, now)
                    .in(BillGoldEggInfo::getId, goldOrderIdList)
            );
        }

        List<Long> orderIdList = goodsOrderInfoList.stream()
                .map(BillGoodsOrderInfo::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        billGoodsOrderInfoService.update(new LambdaUpdateWrapper<>(BillGoodsOrderInfo.class)
                .set(BillGoodsOrderInfo::getOrderStatus, 1)
                .set(BillGoodsOrderInfo::getPayTime, now)
                .set(BillGoodsOrderInfo::getUpdateTime, now)
                .in(BillGoodsOrderInfo::getId, orderIdList)
        );


        List<Long> goodsIdList = goodsOrderInfoList.stream()
                .map(BillGoodsOrderInfo::getGoodsId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        LambdaUpdateWrapper<BillGoodsInfo> updateWrapper = Wrappers.lambdaUpdate(BillGoodsInfo.class)
                .setSql("comment_num = comment_num + 1")
                .in(BillGoodsInfo::getId, goodsIdList);

        billGoodsInfoService.update(updateWrapper);

    }

    private BillBonusHistory handlerBonus(Long currentOrderCount
                                          ,Long customId) {
        // 处理体验金问题
        BillBonusHistory billBonusHistory = billBonusHistoryService.getOne(new LambdaQueryWrapper<>(BillBonusHistory.class)
                .eq(BillBonusHistory::getBonusType, 2)
                .le(BillBonusHistory::getExpiredDoOrder, currentOrderCount)
                .eq(BillBonusHistory::getStatus, 1)
                .eq(BillBonusHistory::getCustomId, customId)
        );
        if (Objects.nonNull(billBonusHistory)) {
            // 更新体验金领取状态
            billBonusHistoryService.update(new LambdaUpdateWrapper<>(BillBonusHistory.class)
                    .set(BillBonusHistory::getStatus, 0)
                    .eq(BillBonusHistory::getId, billBonusHistory.getId()));
        }
        return billBonusHistory;
    }

    private BillFlow handlerParentCommission(BillCustom custom, BigDecimal totalCommission, String orderNo) {
        if (Objects.nonNull(custom.getParentId())) {
            Long parentId = custom.getParentId();
            BillCustomAmount parentAccount = billCustomAmountService.getById(parentId);
            BigDecimal commission = parentAccount.getCommission();
            BillVip billVip = billVipService.getVipListByLevel(custom.getLevel());
            BigDecimal parentCommission = totalCommission.multiply(billVip.getAgentRate())
                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            BigDecimal parentNewBalance = parentAccount.getBalance().add(parentCommission);
            BigDecimal parentNewCommission = commission.add(parentCommission);
            billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .set(BillCustomAmount::getBalance, parentNewBalance)
                    .set(BillCustomAmount::getCommission, parentNewCommission)
                    .eq(BillCustomAmount::getCustomId, parentId)
            );
            // 处理上级佣金流水信息
            BillFlow superiorCommissionFlow = new BillFlow();
            superiorCommissionFlow.setCustomId(parentId);
            superiorCommissionFlow.setUsername(parentAccount.getUsername());
            superiorCommissionFlow.setAmount(parentCommission);
            superiorCommissionFlow.setAfter(parentNewBalance);
            superiorCommissionFlow.setBefore(parentAccount.getBalance());
            superiorCommissionFlow.setType(TYPE_AGENT_TRADE_REBATE.getValue());
            superiorCommissionFlow.setOrderNo(orderNo);
            superiorCommissionFlow.setFlowNo(FlowNoUtil.genFlowNo(custom.getCustomId()));
            superiorCommissionFlow.setCreateTime(System.currentTimeMillis());
            superiorCommissionFlow.setSource((byte) 1);
            superiorCommissionFlow.setAmountType((byte) 1);
            return superiorCommissionFlow;
        }
        return null;
    }


    private BillFlow buildFlow(BigDecimal balance, BigDecimal amount, Long customId,String username,BigDecimal oldBalance,
                               String orderNo, long timeMillis, BalanceChangeTypeEnum type) {
        BillFlow flow = new BillFlow();
        flow.setCustomId(customId);
        flow.setUsername(username);
        flow.setAmount(amount);
        flow.setAfter(balance);
        flow.setBefore(oldBalance);
        flow.setType(type.getValue());
        flow.setFlowNo(FlowNoUtil.genFlowNo(customId));
        flow.setOrderNo(orderNo);
        flow.setSource((byte) 1);
        flow.setAmountType((byte) (type.isAdd() ? 1 : 0));
        flow.setCreateTime(timeMillis);
        return flow;
    }

    private GoodsOrderVO convertVO(List<BillGoodsOrderInfo> orderInfoList, List<BillGoodsInfo> goodsInfo, List<BillGoldEggInfo> goldEggInfo) {
        BillGoodsOrderInfo orderInfo = orderInfoList.get(0);
        GoodsOrderVO orderVO = new GoodsOrderVO();
        orderVO.setId(orderInfo.getId());
        orderVO.setCustomId(orderInfo.getCustomId());
        orderVO.setUsername(orderInfo.getUsername());
        orderVO.setOrderNo(orderInfo.getOrderNo());
        orderVO.setGoodsId(orderInfo.getGoodsId());
        orderVO.setGoodsLogo(orderInfo.getGoodsLogo());
        orderVO.setGoodsName(orderInfo.getGoodsName());
        orderVO.setShopName(orderInfo.getShopName());
        orderVO.setGoodsNum(orderInfo.getGoodsNum());
        orderVO.setGoodsPrice(orderInfo.getGoodsPrice());
        orderVO.setGoodsTotalPrice(orderInfo.getGoodsTotalPrice());
        orderVO.setBalance(orderInfo.getBalance());
        orderVO.setExchangeAmount(orderInfo.getExchangeAmount());
        orderVO.setFrozenAmount(orderInfo.getFrozenAmount());
        orderVO.setCommission(orderInfo.getCommission());
        orderVO.setDoOrderLevel(orderInfo.getDoOrderLevel());
        orderVO.setPayTime(orderInfo.getPayTime());
        orderVO.setOrderStatus(orderInfo.getOrderStatus());
        orderVO.setOrderType(orderInfo.getOrderType());
        orderVO.setContent(orderInfo.getContent());
        orderVO.setInjectionId(orderInfo.getInjectionId());
        orderVO.setCommissionMultiple(orderInfo.getCommissionMultiple());
        orderVO.setGoldOrderId(orderInfo.getGoldOrderId());
        orderVO.setCreateTime(orderInfo.getCreateTime());
        orderVO.setUpdateTime(orderInfo.getUpdateTime());
        long commentNum = (CollectionUtils.isEmpty(goodsInfo) || goodsInfo.get(0).getCommentNum() == null) ? 0 : goodsInfo.get(0).getCommentNum() + 1;
        orderVO.setCommentNum(commentNum);
        if (CollectionUtils.isNotEmpty(goldEggInfo)) {
            BillGoldEggInfo billGoldEggInfo = goldEggInfo.get(0);
            orderVO.setGoldOrderId(billGoldEggInfo.getId());
            orderVO.setBillGoldEggInfo(billGoldEggInfo);
        }
        return orderVO;
    }

    private BillCustomGuard.GuardJson queryGuard(BillCustomGuard customGuard, BillVip billVip, Long currentDoOrder) {
        // 定制化
        List<BillCustomGuard.GuardJson> guardJsonList = Optional.ofNullable(customGuard)
                .map(BillCustomGuard::getGuardJson)
                .map(this::getGuardList)
                .orElse(null);
        if (CollectionUtils.isNotEmpty(guardJsonList)) {
            return guardJsonList.stream()
                    .filter(var -> Objects.equals(var.getOrderCount(), currentDoOrder))
                    .findFirst()
                    .orElse(null);
        }
        guardJsonList = Optional.ofNullable(billVip)
                .map(BillVip::getGuardJson)
                .map(this::getGuardList)
                .orElse(new ArrayList<>());
        return guardJsonList.stream()
                .filter(var -> Objects.equals(var.getOrderCount(), currentDoOrder))
                .findFirst()
                .orElse(null);
    }

    private List<BillCustomGuard.GuardJson> getGuardList(String guard) {
        return Optional.of(guard)
                .filter(StringUtils::isNoneBlank)
                .map(var -> JSONObject.parseObject(var, new TypeReference<List<BillCustomGuard.GuardJson>>() {
                }))
                .orElse(null);
    }

    private List<BillMemberInjectionInfo> queryBillMemberInjectionInfo(Long customId, Long currentDoOrderNum) {
        return billMemberInjectionInfoService.list(
                new LambdaUpdateWrapper<>(BillMemberInjectionInfo.class)
                        .eq(BillMemberInjectionInfo::getCustomId, customId)
                        .eq(BillMemberInjectionInfo::getStatus, 0)
                        .eq(BillMemberInjectionInfo::getJointOrderNumber, currentDoOrderNum)
        );
    }

    private static BigDecimal calculateCommission(BillVip memberLevel, BigDecimal totalExchangeAmount) {
        return totalExchangeAmount
                .multiply(memberLevel.getRate()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    private static long calculateNum(BillGoodsInfo goodsInfo, BigDecimal userBalance) {
        BigDecimal goodsPrice = goodsInfo.getGoodsPrice();
        long theoreticalMaxQuantity = (userBalance.longValue() / goodsPrice.longValue());
        if (theoreticalMaxQuantity == 0L) {
            theoreticalMaxQuantity = 1L;
        }
        return RandomUtil.randomLong(1, Integer.parseInt(String.valueOf(theoreticalMaxQuantity + 2L)));
    }

    private static long calculateNum2(BillGoodsInfo goodsInfo, BigDecimal userBalance) {
        BigDecimal goodsPrice = goodsInfo.getGoodsPrice();
        long theoreticalMaxQuantity = (userBalance.longValue() / goodsPrice.longValue());
        if (theoreticalMaxQuantity == 0L) {
            theoreticalMaxQuantity = 1L;
        }
        return RandomUtil.randomLong(1, Integer.parseInt(String.valueOf(theoreticalMaxQuantity + 1L)));
    }

    private static long calculateSaleOutNumber(BillGoodsInfo goodsInfo, BigDecimal userBalance) {
        BigDecimal goodsPrice = goodsInfo.getGoodsPrice();
        long theoreticalMaxQuantity = (userBalance.longValue() / goodsPrice.longValue());
        if (theoreticalMaxQuantity == 0L) {
            theoreticalMaxQuantity = 1L;
        }
        // 为了简化，这里直接在[minQuantity, maxQuantity]范围内随机选择
        // 实际应用中可能需要更复杂的逻辑来更精确地控制概率分布
        return  theoreticalMaxQuantity + 2;
    }

    @GetMapping("/todayCommission")
    @ApiOperation(value = "今日佣金")
    public AjaxResult todayCommission() {
        TodayCommission commission = new TodayCommission();

        Long customId = SecurityUtils.getCustomId();
        /**统计今天的佣金**/
        Date now = new Date();
        List<BillGoodsOrderInfo> list = billGoodsOrderInfoService.list(new LambdaUpdateWrapper<>(BillGoodsOrderInfo.class)
                .eq(BillGoodsOrderInfo::getCustomId, customId)
                .eq(BillGoodsOrderInfo::getOrderStatus, 1)
                .between(BillGoodsOrderInfo::getCreateTime, DateUtil.beginOfDay(now).getTime(), DateUtil.endOfDay(now).getTime())
        );
        if (CollectionUtils.isNotEmpty(list)) {
            BigDecimal todayCommission = BigDecimal.ZERO;
            for (BillGoodsOrderInfo billGoodsOrderInfo : list) {
                todayCommission = todayCommission.add(billGoodsOrderInfo.getCommission());
            }
            commission.setTodayCommission(todayCommission);
        } else {
            commission.setTodayCommission(BigDecimal.ZERO);
        }
        return AjaxResult.success(commission);
    }


}
