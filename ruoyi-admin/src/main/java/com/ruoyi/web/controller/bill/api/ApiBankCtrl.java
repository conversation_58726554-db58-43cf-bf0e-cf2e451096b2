package com.ruoyi.web.controller.bill.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.bill.domain.BillMemberBankInfo;
import com.ruoyi.bill.service.IBillCustomService;
import com.ruoyi.bill.service.IBillMemberBankInfoService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.BankCardRequest;
import com.ruoyi.common.exception.BusinessErrorCode;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/bank")
public class ApiBankCtrl {
    @Autowired
    private IBillMemberBankInfoService billMemberBankInfoService;

    @Autowired
    private IBillCustomService billCustomService;

    @ApiOperation(value = "银行卡信息", notes = "银行卡信息")
    @GetMapping(value = "/list")
    public AjaxResult queryPageList() {
        List<BillMemberBankInfo> pageList = billMemberBankInfoService.list(
                new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                        .eq(BillMemberBankInfo::getCustomId, SecurityUtils.getCustomId())
                        .orderByDesc(BillMemberBankInfo::getId)
        );
        return AjaxResult.success(pageList);
    }

    @ApiOperation(value = "银行卡信息详情", notes = "银行卡信息详情")
    @GetMapping(value = "/detail")
    public AjaxResult detail() {
        BillMemberBankInfo bankInfo = billMemberBankInfoService.getOne(
                new LambdaUpdateWrapper<>(BillMemberBankInfo.class)
                        .eq(BillMemberBankInfo::getCustomId, SecurityUtils.getCustomId())
                        .orderByDesc(BillMemberBankInfo::getId)
                        .last("limit 1")
        );
        return AjaxResult.success(bankInfo);
    }

    @ApiOperation(value = "新增or修改银行卡", notes = "新增or修改银行卡")
    @PostMapping(value = "/saveOrEdit")
    public AjaxResult saveOrEdit(@RequestBody BankCardRequest request) {
        Long customId = SecurityUtils.getCustomId();
        BillMemberBankInfo bankInfo = billMemberBankInfoService.getOne(new LambdaUpdateWrapper<>(BillMemberBankInfo.class)
                .eq(BillMemberBankInfo::getCustomId, customId)
                .orderByDesc(BillMemberBankInfo::getId)
                .last("limit 1")
        );
        long now = System.currentTimeMillis();
        if (bankInfo == null) {
            long count = billMemberBankInfoService.count(new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                    .eq(BillMemberBankInfo::getBankCardNumber, request.getBankCardNumber())
            );
            if (count > 0) {
                return AjaxResult.error(MessageUtils.message(BusinessErrorCode.getBank_card_has_been_bound.getMessage()));
            }
            bankInfo = new BillMemberBankInfo();
            bankInfo.setCustomId(customId);
            bankInfo.setAccountHolder(request.getAccountHolder());
            bankInfo.setAccountBank(request.getAccountBank());
            bankInfo.setBankCode(request.getBankCode());
            bankInfo.setBankCardNumber(request.getBankCardNumber());
            bankInfo.setWithdrawPassword(request.getWithdrawPassword());
            bankInfo.setUpiId(request.getUpiId());
            bankInfo.setWithdrawType(request.getWithdrawType());
            bankInfo.setWalletType(request.getWalletType());
            bankInfo.setRoutingNumber(request.getRoutingNumber());
            bankInfo.setUpdateTime(now);
            bankInfo.setCreateTime(now);
            billMemberBankInfoService.save(bankInfo);
        } else {
            bankInfo.setAccountHolder(request.getAccountHolder());
            bankInfo.setAccountBank(request.getAccountBank());
            bankInfo.setBankCode(request.getBankCode());
            bankInfo.setBankCardNumber(request.getBankCardNumber());
            bankInfo.setWithdrawPassword(request.getWithdrawPassword());
            bankInfo.setUpiId(request.getUpiId());
            bankInfo.setWithdrawType(request.getWithdrawType());
            bankInfo.setWalletType(request.getWalletType());
            bankInfo.setRoutingNumber(request.getRoutingNumber());
            bankInfo.setUpdateTime(now);
            billMemberBankInfoService.updateById(bankInfo);
        }

        if (StringUtils.isNotBlank(request.getWithdrawPassword())) {
            // 更新提现密码
            billCustomService.update(new LambdaUpdateWrapper<>(BillCustom.class)
                    .set(BillCustom::getWithdrawPassword, request.getWithdrawPassword())
                    .eq(BillCustom::getCustomId, customId)
            );
        }

        return AjaxResult.success();
    }
}
