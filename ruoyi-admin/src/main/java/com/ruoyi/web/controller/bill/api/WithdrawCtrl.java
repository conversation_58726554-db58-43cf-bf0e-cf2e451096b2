package com.ruoyi.web.controller.bill.api;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.bill.domain.*;
import com.ruoyi.bill.domain.enums.BalanceChangeTypeEnum;
import com.ruoyi.bill.service.*;
import com.ruoyi.bill.util.FlowNoUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.redis.RedissonUtil;
import com.ruoyi.common.exception.BusinessErrorCode;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * @description:
 * @author：美式续命
 * @date: 2024/6/30
 * @Copyright：
 */
@Api(tags = "h5-提现")
@RestController
@RequestMapping("/api/bill/withdraw")

public class WithdrawCtrl extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WithdrawCtrl.class);

    @Autowired
    private IBillWithdrawOrderInfoService billWithdrawOrderInfoService;

    @Autowired
    private IBillVipService billVipService;

    @Autowired
    private IBillCustomAmountService billCustomAmountService;

    @Autowired
    private IBillCustomService billCustomService;

    @Autowired
    private IBillMemberBankInfoService billMemberBankInfoService;

    @Autowired
    private IBillFlowService billFlowService;


    @Autowired
    private RedissonUtil redissonUtil;

    @Autowired
    private IBillBonusHistoryService billBonusHistoryService;

    @Autowired
    private IBillGoodsOrderInfoService billGoodsOrderInfoService;



    @ApiOperation(value = "申请提现", notes = "申请提现")
    @PostMapping(value = "/submit")
    @Transactional
    public AjaxResult submit(@RequestBody BillWithdrawOrderInfo billWithdrawOrderInfo) {
        Long customId = SecurityUtils.getCustomId();
        BillMemberBankInfo bankInfo = billMemberBankInfoService.getOne(
                new LambdaUpdateWrapper<>(BillMemberBankInfo.class)
                        .eq(BillMemberBankInfo::getCustomId, customId)
        );
        if (Objects.isNull(bankInfo) || StringUtils.isBlank(bankInfo.getBankCardNumber())) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.bank_card_not_bind.getMessage()));
        }

        if (StringUtils.isBlank(billWithdrawOrderInfo.getPassword())) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.not_set_password.getMessage()));
        }

        BillCustom custom = billCustomService.getById(customId);
        if (StringUtils.equalsIgnoreCase(custom.getCanDraw(), "1")) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.can_not_draw.getMessage()));
        }

        // 提现密码校验
        if (!StringUtils.equals(billWithdrawOrderInfo.getPassword(), custom.getWithdrawPassword())) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.withdraw_password_not_true.getMessage()));
        }
        BillVip vip = billVipService.getVipListByLevel(custom.getLevel());
        BigDecimal drawMin = vip.getDrawMin();
        BigDecimal drawMax = vip.getDrawMax();
        BigDecimal withdrawAmount = billWithdrawOrderInfo.getWithdrawAmount();
        if (withdrawAmount.compareTo(drawMin) < 0 || withdrawAmount.compareTo(drawMax) > 0) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.withdraw_amount_out_range.getMessage()));
        }
        long now = System.currentTimeMillis();
        long count = billWithdrawOrderInfoService.count(new LambdaQueryWrapper<>(BillWithdrawOrderInfo.class)
                .in(BillWithdrawOrderInfo::getOrderStatus, 0, 1)
                .eq(BillWithdrawOrderInfo::getCustomId, customId)
                .between(BillWithdrawOrderInfo::getCreateTime, DateUtil.beginOfDay(new Date()).getTime(), DateUtil.endOfDay(new Date()).getTime())
        );
        if (count >= vip.getDrawDayCount()) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.withdraw_number_limit.getMessage()));
        }
        String key = CacheConstants.getMemberAccountKey();


        redissonUtil.lockProcessBusiness(key, (withDraw, billVip) -> {

            BigDecimal allowedWithdrawalTotalAmount = custom.getAllowedWithdrawalTotalAmount();
            if (allowedWithdrawalTotalAmount == null) {
                allowedWithdrawalTotalAmount = billVip.getAllowedWithdrawalTotalAmount();
            }

            if (allowedWithdrawalTotalAmount != null) {
                BigDecimal withdrawalTotalAmount = billWithdrawOrderInfoService.calculateAllowedWithdrawalTotalAmount(customId, custom.getLevel());
                if (withdrawalTotalAmount != null && withdrawalTotalAmount.compareTo(allowedWithdrawalTotalAmount) >= 0) {
                    throw new BusinessException(BusinessErrorCode.allowed_withdrawal_total_amount);
                }
            }

            if (billWithdrawOrderInfoService.count(new LambdaQueryWrapper<>(BillWithdrawOrderInfo.class)
                    .eq(BillWithdrawOrderInfo::getCustomId, customId)
                    .eq(BillWithdrawOrderInfo::getOrderStatus, 0)
            ) > 0) {
                throw new BusinessException(BusinessErrorCode.withdrawal_proceeding);
            }


            BillCustomAmount account = billCustomAmountService.getOne(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .eq(BillCustomAmount::getCustomId, customId));

            Long currentOrderCount = account.getCurrentOrderCount();
            if (Objects.nonNull(currentOrderCount) && currentOrderCount < vip.getDrawOrderCount()) {
                throw new BusinessException(BusinessErrorCode.withdraw_less_order_total_limit);
            }
            // 查询体验金
            BillBonusHistory billBonusHistory = getBillBonusHistory(account);
            BigDecimal bonusAmount = BigDecimal.ZERO;
            if (Objects.nonNull(billBonusHistory)) {
                bonusAmount = bonusAmount.add(billBonusHistory.getAmount());
            }

            // 余额校验>提现金额+体验金
            BigDecimal balance = account.getBalance();
            if (balance.compareTo(withDraw.getWithdrawAmount().add(bonusAmount)) < 0) {
                throw new BusinessException(BusinessErrorCode.BALANCE_NOT_ENOUGH);
            }
            // 处理用户余额
            balance = balance.subtract(withDraw.getWithdrawAmount());
            // 处理提现订单
            String orderNo = handlerWithdrawOrder(billWithdrawOrderInfo, vip, balance, now, custom,bankInfo);

            if (billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .set(BillCustomAmount::getBalance, balance)
                    .eq(BillCustomAmount::getCustomId, customId)
            )) {
                // 处理流水
                BillFlow entity = new BillFlow();
                entity.setCustomId(customId);
                entity.setType(BalanceChangeTypeEnum.TYPE_DRAW.getValue());
                entity.setOrderNo(orderNo);
                entity.setFlowNo(FlowNoUtil.genFlowNo(customId));
                entity.setAmount(withDraw.getWithdrawAmount());
                entity.setBefore(account.getBalance());
                entity.setAfter(balance);
                entity.setCreateTime(now);
                entity.setSource((byte) 1);
                entity.setAmountType((byte) 0);
                billFlowService.save(entity);
            }

        }, billWithdrawOrderInfo, vip);

        return AjaxResult.success();
    }

    private BillBonusHistory getBillBonusHistory(BillCustomAmount account) {
        return billBonusHistoryService.getOne(new LambdaQueryWrapper<>(BillBonusHistory.class)
                .eq(BillBonusHistory::getBonusType, 2)
                .eq(BillBonusHistory::getExpiredDoOrder, account.getCurrentOrderCount())
                .eq(BillBonusHistory::getStatus, 1)
                .eq(BillBonusHistory::getCustomId, account.getCustomId())
        );
    }

    private String handlerWithdrawOrder(BillWithdrawOrderInfo billWithdrawOrderInfo, BillVip level, BigDecimal newBalance,
                                        long now,BillCustom custom,BillMemberBankInfo bankInfo) {
        // 费率
        BigDecimal drawFee = level.getDrawFee();
        // 提现金额
        BigDecimal withdrawAmount = billWithdrawOrderInfo.getWithdrawAmount();
        // 手续费
        BigDecimal freeAmount = withdrawAmount.multiply(drawFee).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        BillWithdrawOrderInfo withdrawOrderInfo = new BillWithdrawOrderInfo();

        withdrawOrderInfo.setId(billWithdrawOrderInfo.getId());
        withdrawOrderInfo.setCustomId(SecurityUtils.getCustomId());
        withdrawOrderInfo.setUsername(custom.getUsername());
        withdrawOrderInfo.setPhone(custom.getPhone());
        withdrawOrderInfo.setOrderNo("WD" + System.currentTimeMillis() + SecurityUtils.getCustomId() % 4);
        withdrawOrderInfo.setWithdrawAmount(withdrawAmount);
        withdrawOrderInfo.setWithdrawFee(freeAmount);
        withdrawOrderInfo.setBalance(newBalance);
        withdrawOrderInfo.setPayoutMethod(0);
        withdrawOrderInfo.setOrderStatus(0);
        withdrawOrderInfo.setAccountHolder(bankInfo.getAccountHolder());
        withdrawOrderInfo.setAccountBank(bankInfo.getAccountBank());
        withdrawOrderInfo.setBankCode(bankInfo.getBankCode());
        withdrawOrderInfo.setBankCardNumber(bankInfo.getBankCardNumber());
        withdrawOrderInfo.setUpiId(bankInfo.getUpiId());
        withdrawOrderInfo.setWithdrawType(bankInfo.getWithdrawType());
        withdrawOrderInfo.setWalletType(bankInfo.getWalletType());
        withdrawOrderInfo.setRoutingNumber(bankInfo.getRoutingNumber());
        withdrawOrderInfo.setCreateTime(now);
        withdrawOrderInfo.setUpdateTime(now);
        withdrawOrderInfo.setLevel(level.getLevel().intValue());
        billWithdrawOrderInfoService.save(withdrawOrderInfo);
        return withdrawOrderInfo.getOrderNo();
    }

}
