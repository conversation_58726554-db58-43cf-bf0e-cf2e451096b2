
package com.ruoyi.web.controller.bill;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.exception.BusinessErrorCode;
import com.ruoyi.common.utils.MessageUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.bill.domain.BillMemberBankInfo;
import com.ruoyi.bill.domain.condition.BillMemberBankInfoCondition;
import com.ruoyi.bill.service.IBillMemberBankInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.Pagination;

/**
 * 银行卡信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-10
 */
@RestController
@RequestMapping("/bill/bank")
public class BillMemberBankInfoController extends BaseController
{
    @Autowired
    private IBillMemberBankInfoService billMemberBankInfoService;


    /**
     * 获取银行卡信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:bank:query')")
    @GetMapping(value = "/{customId}")
    public AjaxResult getInfo(@PathVariable("customId") String customId) {
        return success(billMemberBankInfoService.getOne(new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                .eq(BillMemberBankInfo::getCustomId, customId)
        ));
    }

    @PreAuthorize("@ss.hasPermi('bill:bank:addOrEdit')")
    @Log(title = "银行卡信息", businessType = BusinessType.INSERT)
    @PostMapping("/addOrEdit")
    public AjaxResult addOrEdit(@RequestBody BillMemberBankInfo billMemberBankInfo)
    {
        if (Objects.isNull(billMemberBankInfo.getCustomId())){
            return error(MessageUtils.message("custom.id.not.null"));
        }
        BillMemberBankInfo bankInfo = billMemberBankInfoService.getOne(new LambdaUpdateWrapper<>(BillMemberBankInfo.class)
                .eq(BillMemberBankInfo::getCustomId, billMemberBankInfo.getCustomId())
                .orderByDesc(BillMemberBankInfo::getId)
                .last("limit 1")
        );

        long count = billMemberBankInfoService.count(new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                .ne(BillMemberBankInfo::getCustomId, billMemberBankInfo.getCustomId())
                .eq(BillMemberBankInfo::getBankCardNumber, billMemberBankInfo.getBankCardNumber())
        );
        if (count > 0) {
            return AjaxResult.error(MessageUtils.message(BusinessErrorCode.getBank_card_has_been_bound.getMessage()));
        }

        if (bankInfo == null) {
            billMemberBankInfoService.save(billMemberBankInfo);
        } else {
            bankInfo.setAccountHolder(billMemberBankInfo.getAccountHolder());
            bankInfo.setAccountBank(billMemberBankInfo.getAccountBank());
            bankInfo.setBankCode(billMemberBankInfo.getBankCode());
            bankInfo.setBankCardNumber(billMemberBankInfo.getBankCardNumber());
            bankInfo.setWithdrawPassword(billMemberBankInfo.getWithdrawPassword());
            bankInfo.setUpiId(billMemberBankInfo.getUpiId());
            bankInfo.setWalletType(billMemberBankInfo.getWalletType());
            bankInfo.setWithdrawType(billMemberBankInfo.getWithdrawType());
            bankInfo.setRoutingNumber(billMemberBankInfo.getRoutingNumber());
            billMemberBankInfoService.updateById(bankInfo);
        }
        return toAjax(true);
    }

}
