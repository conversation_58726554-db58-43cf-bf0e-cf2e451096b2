package com.ruoyi.web.controller.bill.api;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.bill.constant.BillCustomConstant;
import com.ruoyi.bill.domain.BillCustomAmount;
import com.ruoyi.bill.domain.BillVip;
import com.ruoyi.bill.domain.vo.BillLoginConfigVo;
import com.ruoyi.bill.service.*;
import com.ruoyi.bill.util.EmailUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.BillCustomDto;
import com.ruoyi.common.core.domain.dto.ShopDto;
import com.ruoyi.common.core.domain.dto.WalletDTO;
import com.ruoyi.common.core.domain.dto.GoogleAuthInfo;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.BillSystemConfigRequest;
import com.ruoyi.common.core.domain.model.LoginCustom;
import com.ruoyi.common.enums.CustomStatus;
import com.ruoyi.common.enums.SysConfigEnum;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SysConfigUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.validator.ValidatorUtils;
import com.ruoyi.common.validator.group.AddGroup;
import com.ruoyi.common.validator.group.DefaultGroup;
import com.ruoyi.framework.web.service.BillLoginService;
import com.ruoyi.framework.web.service.CustomTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 会员信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@RestController
@RequestMapping("/api/bill/custom")
public class ApiBillCustomCtrl extends BaseController {


    private Logger logger = LoggerFactory.getLogger(ApiBillCustomCtrl.class);

    @Autowired
    private IBillCustomService billCustomService;
    @Autowired
    private BillLoginService billLoginService;

    @Autowired
    private IBillCustomAmountService billCustomAmountService;

    @Autowired
    private IBillSystemConfigService billSystemConfigService;
    @Autowired
    private IBillBonusHistoryService billBonusHistoryService;

    @Autowired
    private IBillVipService billVipService;

    @Autowired
    private CustomTokenService customTokenService;

    /**
     * 登录方法
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginCustom loginCustom) {
        AjaxResult ajax = AjaxResult.success();
        // 效验数据
        ValidatorUtils.validateEntity(loginCustom, AddGroup.class);
        // 生成令牌
        String token = billLoginService.login(loginCustom);
        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOKEN, token);
        data.put(Constants.CUSTOM_ID, loginCustom.getCustomId());
        ajax.put(AjaxResult.DATA_TAG, data);
        return ajax;
    }

    /**
     * 登录方法
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/phone/login")
    public AjaxResult phoneLogin(@RequestBody LoginCustom loginCustom) {
        AjaxResult ajax = AjaxResult.success();
        // 效验数据
        ValidatorUtils.validateEntity(loginCustom, DefaultGroup.class);
        BillCustom billCustom = billCustomService.selectCustomByPhone(loginCustom.getPhone(), loginCustom.getPhoneCode());
        if (billCustom == null) {
            throw new UserPasswordNotMatchException();
        }
        loginCustom.setUsername(billCustom.getUsername());
        // 生成令牌
        String token = billLoginService.login(loginCustom);
        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOKEN, token);
        data.put(Constants.CUSTOM_ID, loginCustom.getCustomId());
        ajax.put(AjaxResult.DATA_TAG, data);
        return ajax;
    }
    /**
     * 注册
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/register/phone")
    public AjaxResult phoneRegister(@RequestBody LoginCustom loginCustom, HttpServletRequest request) {
        // 效验数据
        ValidatorUtils.validateEntity(loginCustom, DefaultGroup.class);
        //校验验证码
        billLoginService.validateCaptcha(loginCustom.getPhone(), loginCustom.getCode(), loginCustom.getUuid());
        //校验账号唯一性
        billCustomService.checkPhoneUnique(loginCustom.getPhone(), loginCustom.getPhoneCode());
        //校验邀请码
        billCustomService.verifyInviteCode(loginCustom);
        String ipAddr = IpUtils.getIpAddr(request);
        loginCustom.setIpaddr(ipAddr);
        BillCustom billCustom = billCustomService.convertToBillCustom(loginCustom);
        billCustomService.generateUsername(billCustom);
        //校验注册规则
        billCustomService.verifyRegisterRule(billCustom, BillCustomConstant.REGISTER_PHONE);
        //注册
        billCustomService.register(billCustom);
        //领取体验金&注册赠金
        handleReceiveBonus(billCustom);
        return success();
    }
    /**
     * 注册
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody LoginCustom loginCustom, HttpServletRequest request) {
        //  效验数据
        ValidatorUtils.validateEntity(loginCustom, AddGroup.class);
        //  校验验证码
        billLoginService.validateCaptcha(loginCustom.getUsername(), loginCustom.getCode(), loginCustom.getUuid());
        //  校验用户名唯一性
        billCustomService.checkUsernameUnique(loginCustom.getUsername());
        //  校验邮箱唯一性
        billCustomService.checkEmailUnique(loginCustom.getEmail());
        //  校验手机号唯一性
        billCustomService.checkPhoneUnique(loginCustom.getPhone(), loginCustom.getPhoneCode());
        //  校验邀请码
        billCustomService.verifyInviteCode(loginCustom);
        //  设置注册IP
        loginCustom.setIpaddr(IpUtils.getIpAddr(request));
        BillCustom billCustom = billCustomService.convertToBillCustom(loginCustom);
        //  校验注册规则
        billCustomService.verifyRegisterRule(billCustom, BillCustomConstant.REGISTER_USERNAME);
        //  注册
        billCustomService.register(billCustom);
        //  领取体验金&注册赠金
        handleReceiveBonus(billCustom);
        return success();
    }

    /**
     * 领取体验金&注册赠金
     * @param billCustom
     */
    private void handleReceiveBonus(BillCustom billCustom) {
        try {
            billBonusHistoryService.receiveExperienceBonus(billCustom);
        }catch (Exception e) {
            logger.error("领取奖金失败.", e);
        }
    }

    @GetMapping("/detail")
    public AjaxResult detail() {
        Long customId = SecurityUtils.getCustomId();
        BillCustom custom = billCustomService.getById(customId);
        BillCustomAmount customAmount = billCustomAmountService.getById(customId);
        BillSystemConfigRequest conf = billSystemConfigService.getConfig(1L);
        BillVip vipListByLevel = billVipService.getVipListByLevel(custom.getLevel());
        BillCustomDto billCustomDto = new BillCustomDto();
        billCustomDto.setCustomId(custom.getCustomId());
        billCustomDto.setUsername(custom.getUsername());
        billCustomDto.setInviteCode(custom.getInviteCode());
        billCustomDto.setEmail(custom.getEmail());
        billCustomDto.setPhoneCode(custom.getPhoneCode());
        billCustomDto.setPhone(custom.getPhone());
        billCustomDto.setAvatar(custom.getAvatar());
        billCustomDto.setLevel(custom.getLevel());
        billCustomDto.setParentId(custom.getParentId());
        billCustomDto.setParentUsername(custom.getParentUsername());
        billCustomDto.setStatus(custom.getStatus());
        billCustomDto.setOnline(custom.getOnline());
        billCustomDto.setRegisterTime(custom.getRegisterTime());
        billCustomDto.setRegisterIp(custom.getRegisterIp());
        billCustomDto.setLoginTime(custom.getLoginTime());
        billCustomDto.setLoginIp(custom.getLoginIp());
        billCustomDto.setCanDraw(custom.getCanDraw());
        billCustomDto.setScore(custom.getScore());
        billCustomDto.setVipName(vipListByLevel.getName());
        billCustomDto.setGroupName(custom.getGroupName());
        billCustomDto.setHistoryText(custom.getHistoryText());
        billCustomDto.setBalance(customAmount.getBalance());
        billCustomDto.setDeposit(customAmount.getDeposit());
        billCustomDto.setDepositInterest(customAmount.getDepositInterest());
        billCustomDto.setCurrentOrderCount(customAmount.getCurrentOrderCount());
        billCustomDto.setTotalOrderCount(customAmount.getTotalOrderCount());
        billCustomDto.setFrozenAmount(customAmount.getFrozenAmount());
        billCustomDto.setRecharge(customAmount.getRecharge());
        billCustomDto.setRechargeCount(customAmount.getRechargeCount());
        billCustomDto.setDraw(customAmount.getDraw());
        billCustomDto.setDrawCount(customAmount.getDrawCount());
        billCustomDto.setAuditingRecharge(customAmount.getAuditingRecharge());
        billCustomDto.setAuditingDraw(customAmount.getAuditingDraw());
        billCustomDto.setCommission(customAmount.getCommission());
        billCustomDto.setCurrency(conf.getCurrency());
        billCustomDto.setUserPoints(customAmount.getUserPoints());
        billCustomDto.setGender(custom.getGender());
        ShopDto shopDto = billCustomService.selectSellerShopNum(customId);
        Optional.ofNullable(shopDto)
                .ifPresent(var -> {
                    billCustomDto.setShopName(var.getShopName());
                    billCustomDto.setShopRate(var.getShopRate());
                });
        custom.queryExtra(BillCustomConstant.modifyUsername);

        String modifyUsernameTime = custom.queryExtra(BillCustomConstant.modifyUsername);
        if (StringUtils.isNotEmpty(modifyUsernameTime)) {
            long l = Long.parseLong(modifyUsernameTime);
            if (l - System.currentTimeMillis() >= 365L * 24 * 60 * 60 * 1000) {
                billCustomDto.setEditShopName(true);
            } else {
                billCustomDto.setEditShopName(false);
            }
        } else {
            billCustomDto.setEditShopName(true);
        }
        billCustomDto.setOrderCount(vipListByLevel.getOrderCount());
        return AjaxResult.success(billCustomDto);
    }

    @PostMapping("/modifyShopName")
    public AjaxResult modifyShopName(@RequestBody LoginCustom loginCustom) {
        billCustomService.modifyShopName(loginCustom);
        return AjaxResult.success();
    }

    @PostMapping("/modifyEmail")
    public AjaxResult modifyEmail(@RequestBody LoginCustom loginCustom) {
        EmailUtil.isValidEmail(loginCustom.getEmail());
        billCustomService.modifyEmail(loginCustom);
        return AjaxResult.success();
    }



    @GetMapping("/walletInfo")
    public AjaxResult walletInfo() {
        Long customId = SecurityUtils.getCustomId();
        WalletDTO walletInfo = billCustomAmountService.walletInfo(customId);
        return AjaxResult.success(walletInfo);
    }

    /**
     * 谷歌授权登录
     *
     * @param googleAuthInfo 谷歌授权信息
     * @return 结果
     */
    @PostMapping("/googleAuth")
    public AjaxResult googleAuth(@RequestBody GoogleAuthInfo googleAuthInfo) {
        AjaxResult ajax = AjaxResult.success();

        // 处理谷歌授权登录
        BillCustom billCustom = billCustomService.handleGoogleAuth(googleAuthInfo);

        // 生成令牌
        LoginCustom loginCustom = new LoginCustom();
        loginCustom.setUsername(billCustom.getUsername());
        loginCustom.setCustomId(billCustom.getCustomId());
        String token = customTokenService.createToken(loginCustom);

        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOKEN, token);
        data.put(Constants.CUSTOM_ID, billCustom.getCustomId());
        // 添加是否绑定邀请码的标志
        data.put("hasInviteCode", billCustom.getParentId() != null);
        ajax.put(AjaxResult.DATA_TAG, data);
        //领取体验金&注册赠金
        handleReceiveBonus(billCustom);
        return ajax;
    }

    /**
     * 绑定邀请码
     *
     * @param params 包含邀请码的参数
     * @return 结果
     */
    @PostMapping("/bindInviteCode")
    public AjaxResult bindInviteCode(@RequestBody Map<String, String> params) {
        String inviteCode = params.get("inviteCode");
        if (StringUtils.isEmpty(inviteCode)) {
            return AjaxResult.error(MessageUtils.message("custom.register.inviteCodeEmpty"));
        }

        Long customId = SecurityUtils.getCustomId();
        try {
            billCustomService.bindInviteCode(customId, inviteCode);
            return AjaxResult.success();
        } catch (BusinessException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 邮箱登录方法
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/email/login")
    public AjaxResult emailLogin(@RequestBody LoginCustom loginCustom) {
        AjaxResult ajax = AjaxResult.success();
        // 效验数据
        if (StringUtils.isEmpty(loginCustom.getEmail())) {
            return AjaxResult.error(MessageUtils.message("custom.register.emailEmpty"));
        }
        if (StringUtils.isEmpty(loginCustom.getPassword())) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordEmpty"));
        }

        // 查询用户
        BillCustom billCustom = billCustomService.selectCustomByEmail(loginCustom.getEmail());
        if (billCustom == null) {
            throw new UserPasswordNotMatchException();
        }

        if (!StringUtils.equals(billCustom.getPasswordText(), loginCustom.getPassword())) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordError"));
        }

        // 生成令牌
        loginCustom.setUsername(billCustom.getUsername());
        loginCustom.setCustomId(billCustom.getCustomId());
        String token = customTokenService.createToken(loginCustom);

        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOKEN, token);
        data.put(Constants.CUSTOM_ID, billCustom.getCustomId());
        // 添加是否绑定邀请码的标志
        data.put("hasInviteCode", billCustom.getParentId() != null);
        ajax.put(AjaxResult.DATA_TAG, data);
        return ajax;
    }

    /**
     * 邮箱登录方法
     *
     * @param loginCustom 登录信息
     * @return 结果
     */
    @PostMapping("/username/login")
    public AjaxResult usernameLogin(@RequestBody LoginCustom loginCustom) {
        AjaxResult ajax = AjaxResult.success();
        // 效验数据
        String username = loginCustom.getUsername();
        if (StringUtils.isEmpty(username)) {
            return AjaxResult.error(MessageUtils.message("custom.register.usernameExist"));
        }
        if (StringUtils.isEmpty(loginCustom.getPassword())) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordEmpty"));
        }

        // 查询用户
        BillCustom billCustom = billCustomService.selectCustomByUsername(username);
        if (billCustom == null) {
            throw new UserPasswordNotMatchException();
        }

        if (!StringUtils.equals(billCustom.getPasswordText(), loginCustom.getPassword())) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordError"));
        }

        BillLoginConfigVo loginConfigVo = SysConfigUtils.getConfig(SysConfigEnum.BILL_LOGIN_CONFIG, BillLoginConfigVo.class);
        if (loginConfigVo.getActivate() != null && loginConfigVo.getActivate()) {
            if (!CustomStatus.ACTIVE.getCode().equals(billCustom.getStatus())) {
                logger.info("客户端登录账户：{} 未激活.", username);
                throw new ServiceException(MessageUtils.message("custom.login.status.notActive"));
            }
        }
        // 生成令牌
        loginCustom.setUsername(billCustom.getUsername());
        loginCustom.setCustomId(billCustom.getCustomId());
        String token = customTokenService.createToken(loginCustom);

        Map<String, Object> data = new HashMap<>();
        data.put(Constants.TOKEN, token);
        data.put(Constants.CUSTOM_ID, billCustom.getCustomId());
        // 添加是否绑定邀请码的标志
        data.put("hasInviteCode", billCustom.getParentId() != null);
        ajax.put(AjaxResult.DATA_TAG, data);
        return ajax;
    }


    @PostMapping("/modifyWithdrawPassword")
    public AjaxResult modifyWithdrawPassword(@RequestBody Map<String, String> params) {
        String oldPassword = params.get("oldPassword");

        String newPassword = params.get("newPassword");
        String confirmNewPassword = params.get("confirmNewPassword");
        if (StringUtils.isEmpty(oldPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordEmpty"));
        }
        if (StringUtils.isEmpty(newPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordEmpty"));
        }
        if (!StringUtils.equals(newPassword, confirmNewPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordNotMatch"));
        }
        Long customId = SecurityUtils.getCustomId();
        BillCustom billCustom = billCustomService.getById(customId);
        if (!StringUtils.equals(billCustom.getWithdrawPassword(), oldPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordError"));
        }
        billCustomService.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getWithdrawPassword, newPassword)
                .eq(BillCustom::getCustomId, customId)
        );
        return AjaxResult.success();
    }

    @PostMapping("/modifyPassword")
    public AjaxResult modifyPassword(@RequestBody Map<String, String> params) {
        String oldPassword = params.get("oldPassword");
        String newPassword = params.get("newPassword");
        String confirmNewPassword = params.get("confirmNewPassword");

        if (!StringUtils.equals(newPassword, confirmNewPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.passwordNotMatch"));
        }
        Long customId = SecurityUtils.getCustomId();
        BillCustom billCustom = billCustomService.getById(customId);
        if (!StringUtils.equals(billCustom.getPasswordText(), oldPassword)) {
            return AjaxResult.error(MessageUtils.message("custom.register.oldPasswordError"));
        }
        billCustomService.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getPasswordText, newPassword)
                .eq(BillCustom::getCustomId, customId)
        );
        return AjaxResult.success();
    }

}
