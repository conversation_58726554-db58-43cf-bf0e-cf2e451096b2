package com.ruoyi.web.controller.bill;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.bill.domain.BillPrizeConfig;
import com.ruoyi.bill.domain.condition.BillPrizeConfigCondition;
import com.ruoyi.bill.service.IBillPrizeConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.Pagination;

/**
 * 奖品配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@RestController
@RequestMapping("/bill/config")
public class BillPrizeConfigController extends BaseController
{
    @Autowired
    private IBillPrizeConfigService billPrizeConfigService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询奖品配置列表
     */
    @PreAuthorize("@ss.hasPermi('bill:config:list')")
    @GetMapping("/list")
    public Pagination<BillPrizeConfig> list(BillPrizeConfigCondition condition)
    {
        condition.setOrderDesc("prize_id");
        Pagination<BillPrizeConfig> page = billPrizeConfigService.selectPageByCondition(condition);
        return page;
    }

    /**
     * 导出奖品配置列表
     */
    @PreAuthorize("@ss.hasPermi('bill:config:export')")
    @Log(title = "奖品配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BillPrizeConfigCondition condition)
    {
        List<BillPrizeConfig> list = billPrizeConfigService.selectListByCondition(condition);
        ExcelUtil<BillPrizeConfig> util = new ExcelUtil<>(BillPrizeConfig.class);
        util.exportExcel(response, list, "奖品配置数据");
    }

    /**
     * 获取奖品配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:config:query')")
    @GetMapping(value = "/{prizeId}")
    public AjaxResult getInfo(@PathVariable("prizeId") Long prizeId)
    {
        return success(billPrizeConfigService.getById(prizeId));
    }

    /**
     * 新增奖品配置
     */
    @PreAuthorize("@ss.hasPermi('bill:config:add')")
    @Log(title = "奖品配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BillPrizeConfig billPrizeConfig)
    {
        return toAjax(billPrizeConfigService.save(billPrizeConfig));
    }

    /**
     * 修改奖品配置
     */
    @PreAuthorize("@ss.hasPermi('bill:config:edit')")
    @Log(title = "奖品配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BillPrizeConfig billPrizeConfig)
    {
        return toAjax(billPrizeConfigService.updateById(billPrizeConfig));
    }

    /**
     * 删除奖品配置
     */
    @PreAuthorize("@ss.hasPermi('bill:config:remove')")
    @Log(title = "奖品配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{prizeIds}")
    public AjaxResult remove(@PathVariable Long[] prizeIds)
    {
        return toAjax(billPrizeConfigService.removeByIds(Arrays.asList(prizeIds)));
    }


    @PreAuthorize("@ss.hasPermi('bill:config:edit')")
    @Log(title = "奖品配置", businessType = BusinessType.UPDATE)
    @PostMapping("/saveTime")
    public AjaxResult saveTime(@RequestBody Map billPrizeConfig) {
        redisCache.setCacheObject("prize:countdown:startTime", billPrizeConfig.get("startTime"));
        redisCache.setCacheObject("prize:countdown:endTime", billPrizeConfig.get("endTime"));
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('bill:config:edit')")
    @Log(title = "奖品配置", businessType = BusinessType.UPDATE)
    @GetMapping("/queryTime")
    public AjaxResult queryTime() {
        Object startTime = redisCache.getCacheObject("prize:countdown:startTime");
        Object endTime = redisCache.getCacheObject("prize:countdown:endTime");
        if (Objects.isNull(startTime)||Objects.isNull(endTime)){
            return AjaxResult.success();
        }
        Map<String, Object> data = Map.of("startTime", startTime, "endTime", endTime);
        return AjaxResult.success(data);
    }
}
