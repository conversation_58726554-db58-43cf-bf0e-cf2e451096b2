package com.ruoyi.web.controller.bill;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.bill.domain.*;
import com.ruoyi.bill.domain.enums.BalanceChangeTypeEnum;
import com.ruoyi.bill.service.*;
import com.ruoyi.bill.util.FlowNoUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.redis.RedissonUtil;
import com.ruoyi.common.utils.MessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.bill.domain.condition.BillWithdrawOrderInfoCondition;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.Pagination;

/**
 * 提现订单信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
@RestController
@RequestMapping("/bill/withdraw")
public class BillWithdrawOrderInfoController extends BaseController {
    @Autowired
    private IBillWithdrawOrderInfoService billWithdrawOrderInfoService;

    @Autowired
    private IBillCustomAmountService billCustomAmountService;

    @Autowired
    private IBillFlowService billFlowService;
    @Autowired
    private RedissonUtil redissonUtil;


    @Autowired
    private IBillCustomService billCustomService;

    @Autowired
    private IBillMemberBankInfoService billMemberBankInfoService;

    /**
     * 查询提现订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:list')")
    @GetMapping("/list")
    public Pagination<BillWithdrawOrderInfo> list(BillWithdrawOrderInfoCondition condition) {
        condition.setOrderDesc("id");
        Pagination<BillWithdrawOrderInfo> page = billWithdrawOrderInfoService.selectPageByCondition(condition);
        List<BillWithdrawOrderInfo> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            Map<Long, BillCustom> parentMaps = new HashMap<>();
            Map<Long, BillCustom> customMaps = new HashMap<>();
            List<Long> customIds = rows.stream().map(BillWithdrawOrderInfo::getCustomId).collect(Collectors.toList());
            List<BillCustom> billCustoms = billCustomService.listByIds(customIds);
            if (CollectionUtils.isNotEmpty(billCustoms)) {
                customMaps = billCustoms.stream()
                            .collect(Collectors.toMap(BillCustom::getCustomId, v -> v, (v1, v2) -> v2));

                List<Long> parentIds = billCustoms.stream()
                        .map(BillCustom::getParentId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(parentIds)) {
                    List<BillCustom> parentCustoms = billCustomService.listByIds(parentIds);
                    if (CollectionUtils.isNotEmpty(parentCustoms)) {
                        parentMaps = parentCustoms.stream()
                                .collect(Collectors.toMap(BillCustom::getCustomId, v -> v, (v1, v2) -> v2));
                    }
                }
            }
            for (BillWithdrawOrderInfo row : rows) {
                BillCustom custom = customMaps.get(row.getCustomId());
                if (Objects.nonNull(custom)) {
                    row.setCustom(custom);
                    if (Objects.nonNull(custom.getParentId())) {
                        row.setParentCustom(parentMaps.get(custom.getParentId()));
                    }
                }

                Long one2 = billMemberBankInfoService.count(new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                        .eq(BillMemberBankInfo::getBankCardNumber, row.getBankCardNumber())
                        .ne(BillMemberBankInfo::getCustomId, row.getCustomId())
                );
                
                row.setSameBankCardNumber(one2);

                Long one3 = billMemberBankInfoService.count(new LambdaQueryWrapper<>(BillMemberBankInfo.class)
                        .eq(BillMemberBankInfo::getAccountHolder, row.getAccountHolder())
                        .ne(BillMemberBankInfo::getCustomId, row.getCustomId())
                );
                row.setSameName(one3);
            }


        }
        return page;
    }

    /**
     * 导出提现订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:export')")
    @Log(title = "提现订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BillWithdrawOrderInfoCondition condition) {
        List<BillWithdrawOrderInfo> list = billWithdrawOrderInfoService.selectListByCondition(condition);
        ExcelUtil<BillWithdrawOrderInfo> util = new ExcelUtil<>(BillWithdrawOrderInfo.class);
        util.exportExcel(response, list, "提现订单信息数据");
    }

    /**
     * 获取提现订单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(billWithdrawOrderInfoService.getById(id));
    }

    /**
     * 新增提现订单信息
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:add')")
    @Log(title = "提现订单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BillWithdrawOrderInfo billWithdrawOrderInfo) {
        return toAjax(billWithdrawOrderInfoService.save(billWithdrawOrderInfo));
    }

    /**
     * 修改提现订单信息
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:edit')")
    @Log(title = "提现订单信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BillWithdrawOrderInfo billWithdrawOrderInfo) {
        return toAjax(billWithdrawOrderInfoService.updateById(billWithdrawOrderInfo));
    }

    /**
     * 删除提现订单信息
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:remove')")
    @Log(title = "提现订单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(billWithdrawOrderInfoService.removeByIds(Arrays.asList(ids)));
    }

    @PreAuthorize("@ss.hasPermi('bill:withdraw:pass')")
    @Log(title = "提现订单信息", businessType = BusinessType.DELETE)
    @RequestMapping(value = "/pass", method = {RequestMethod.POST})
    public AjaxResult pass(@RequestBody BillWithdrawOrderInfo billWithdrawOrderInfo) {

        BillWithdrawOrderInfo one = billWithdrawOrderInfoService.getById(billWithdrawOrderInfo.getId());
        if (Objects.isNull(one)) {
            return AjaxResult.error(MessageUtils.message("the.order.does.not.exist"));
        }
        if (!Objects.equals(one.getOrderStatus(), 0)) {
            return AjaxResult.error(MessageUtils.message("the.order.has.been.processed"));
        }
        billWithdrawOrderInfo.setOrderStatus(1);
        long now = System.currentTimeMillis();
        billWithdrawOrderInfo.setUpdateTime(now);
        billWithdrawOrderInfoService.updateById(billWithdrawOrderInfo);

        String key = CacheConstants.getMemberAccountKey(one.getCustomId());
        redissonUtil.lockProcessBusiness(key, (withDraw) -> {
            Long customId = withDraw.getCustomId();
            BillCustomAmount account = billCustomAmountService.getOne(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .eq(BillCustomAmount::getCustomId, customId));
            BigDecimal totalDraw = account.getDraw();
            if (Objects.isNull(totalDraw)) {
                totalDraw = BigDecimal.ZERO;
            }
            totalDraw = totalDraw.add(withDraw.getWithdrawAmount());

            Long num = account.getDrawCount() == null ? 0 : account.getDrawCount();
            billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .set(BillCustomAmount::getDraw, totalDraw)
                    .set(BillCustomAmount::getDrawCount, num + 1)
                    .eq(BillCustomAmount::getCustomId, customId));

        }, one);
        return AjaxResult.success();
    }

    /**
     * 编辑
     *
     * @param billWithdrawOrderInfo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('bill:withdraw:notPassed')")
    @Log(title = "提现订单信息", businessType = BusinessType.DELETE)
    @RequestMapping(value = "/notPassed", method = {RequestMethod.POST})
    public AjaxResult notPassed(@RequestBody BillWithdrawOrderInfo billWithdrawOrderInfo) {
        BillWithdrawOrderInfo one = billWithdrawOrderInfoService.getById(billWithdrawOrderInfo.getId());
        if (Objects.isNull(one)) {
            return AjaxResult.error(MessageUtils.message("the.order.does.not.exist"));
        }
        if (!Objects.equals(one.getOrderStatus(), 0)) {
            return AjaxResult.error(MessageUtils.message("the.order.has.been.processed"));
        }

        String key = CacheConstants.getMemberAccountKey(one.getCustomId());
        redissonUtil.lockProcessBusiness(key, (withDraw) -> {
            Long customId = withDraw.getCustomId();
            long now = System.currentTimeMillis();
            billWithdrawOrderInfoService.update(new LambdaUpdateWrapper<>(BillWithdrawOrderInfo.class)
                    .set(BillWithdrawOrderInfo::getOrderStatus, 2)
                    .set(StringUtils.isNotBlank(billWithdrawOrderInfo.getReason()),
                            BillWithdrawOrderInfo::getReason, billWithdrawOrderInfo.getReason())
                    .set(BillWithdrawOrderInfo::getUpdateTime, now)
                    .eq(BillWithdrawOrderInfo::getCustomId, customId)
                    .eq(BillWithdrawOrderInfo::getId, withDraw.getId())
            );
            BillCustomAmount account = billCustomAmountService.getOne(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .eq(BillCustomAmount::getCustomId, customId));
            BigDecimal newBalance = account.getBalance().add(withDraw.getWithdrawAmount());
            billCustomAmountService.update(new LambdaUpdateWrapper<>(BillCustomAmount.class)
                    .set(BillCustomAmount::getBalance, newBalance)
                    .eq(BillCustomAmount::getCustomId, customId));
            /**处理流水信息**/
            BillFlow entity = new BillFlow();
            entity.setCustomId(customId);
            entity.setUsername(withDraw.getUsername());
            entity.setType(BalanceChangeTypeEnum.TYPE_DRAW_BACK.getValue());
            entity.setFlowNo(FlowNoUtil.genFlowNo(customId));
            entity.setOrderNo(withDraw.getOrderNo());
            entity.setAmount(withDraw.getWithdrawAmount());
            entity.setBefore(account.getBalance());
            entity.setAfter(newBalance);
            entity.setSource((byte) 1);
            entity.setAmountType((byte) 1);
            entity.setCreateTime(now);
            billFlowService.save(entity);
        }, one);

        return AjaxResult.success();
    }

    @GetMapping(value = "/withdrawNotice")
    public AjaxResult withdrawNotice() {
        // 今日提现待处理总数
        Date date = new Date();
        long count = billWithdrawOrderInfoService.count(new LambdaQueryWrapper<>(BillWithdrawOrderInfo.class)
                .eq(BillWithdrawOrderInfo::getOrderStatus, 0)
                .between(BillWithdrawOrderInfo::getCreateTime, DateUtil.beginOfDay(date).getTime(), DateUtil.endOfDay(date).getTime())
        );
        // 提现待处理总数
        long count1 = billWithdrawOrderInfoService.count(new LambdaQueryWrapper<>(BillWithdrawOrderInfo.class)
                .eq(BillWithdrawOrderInfo::getOrderStatus, 0)
        );
        HashMap<Object, Object> data = new HashMap<>();
        data.put("todayCount", count);
        data.put("totalCount", count1);
        return AjaxResult.success(data);
    }

}
