package com.ruoyi.common.core.domain.entity;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.model.SimpleModel;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.validator.group.AddGroup;
import com.ruoyi.common.validator.group.DefaultGroup;
import com.ruoyi.common.validator.group.UpdateGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.MapUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 会员信息对象 bill_custom
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_custom")
public class BillCustom extends SimpleModel
{
    private static final long serialVersionUID = 1L;

    /** 客户ID */
    @TableId(type = IdType.AUTO)
    @NotNull(groups = {UpdateGroup.class}, message = "{custom.register.customIdEmpty}")
    private Long customId;

    /** 客户账号 */
    @Excel(name = "客户账号")
    @NotBlank(groups = {AddGroup.class}, message = "{custom.register.usernameEmpty}")
    private String username;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 客户邮箱 */
    @Excel(name = "客户邮箱")
    private String email;

    /** 手机区号 */
    @Excel(name = "手机区号")
    @NotBlank(groups = {DefaultGroup.class}, message = "{custom.register.phoneCodeEmpty}")
    private String phoneCode;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @NotBlank(groups = {DefaultGroup.class}, message = "{custom.register.phoneEmpty}")
    private String phone;

    /** 头像地址 */
    @Excel(name = "头像地址")
    private String avatar;

    /** vip等级 */
    @Excel(name = "vip等级")
    @NotNull(groups = {AddGroup.class,DefaultGroup.class}, message = "{custom.register.levelEmpty}")
    private Long level;

    /** 密码 */
    @Excel(name = "密码")
    @NotBlank(groups = {AddGroup.class,DefaultGroup.class}, message = "{custom.register.passwordEmpty}")
    private String password;

    /** 密码明文 */
    private String passwordText;

    /** 上级客户id */
    @Excel(name = "上级客户id")
    private Long parentId;

    /** 上级客户账号 */
    private String parentUsername;

    /** 帐号状态（0未激活 1已激活）, 字典bill_custom_status */
    @Excel(name = "帐号状态", readConverterExp = "0=未激活,1=已激活")
    private String status;

    /** 在线状态（0离线 1在线）,字典bill_custom_online */
    @Excel(name = "在线状态", readConverterExp = "0=离线,1=在线")
    private String online;

    /** 客户类型（1真人 2假人），字典bill_custom_type */
    @Excel(name = "客户类型", readConverterExp = "1=真人,2=假人")
    private String type;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long registerTime;

    /** 注册IP */
    @Excel(name = "注册IP")
    private String registerIp;

    /** 登入时间 */
    @Excel(name = "登入时间")
    private Long loginTime;

    /** 登录ip */
    @Excel(name = "登录ip")
    private String loginIp;

    /** 客服组id */
    @Excel(name = "客服组id")
    @NotNull(groups = {AddGroup.class,DefaultGroup.class}, message = "{custom.register.serviceTeamIdEmpty}")
    private Long serviceTeamId;

    /** 是否可提现（0正常 1禁止）,字典bill_custom_can_draw */
    private String canDraw;

    /** 信誉分 */
    @Excel(name = "信誉分")
    private Long score;

    /** vip名称 */
    @TableField(exist = false)
    private String vipName;

    /** 客服组名称 */
    @TableField(exist = false)
    private String groupName;

    private String remark;

    /** 匹配范围最小值 */
    @Excel(name = "匹配范围最小值")
    private BigDecimal rangeMin;

    /** 匹配范围最大值 */
    @Excel(name = "匹配范围最大值")
    private BigDecimal rangeMax;

    /** 历史文本 */
    private String historyText;

    private BigDecimal allowedWithdrawalTotalAmount;

    private String extra;

    @TableField(exist = false)
    private String shopName;

    private String googleId;

    /** 2->女；1->男 */
    private String gender;

    private String withdrawPassword;

    public String queryExtra(String key) {
        Map<Object, Object> extraMap;
        if (StringUtils.isNotBlank(extra)) {
            extraMap = JSON.parseObject(extra, Map.class);
            if (extraMap.containsKey(key)) {
                return extraMap.get(key).toString();
            }
            return null;
        }
        return null;
    }

}
