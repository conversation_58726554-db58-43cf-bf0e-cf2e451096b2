package com.ruoyi.common.core.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.entity.BillCustom;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 会员信息对象 bill_custom
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillCustomDto extends BillCustom {
    private static final long serialVersionUID = 1L;


    /**
     * 余额
     */
    @Excel(name = "余额")
    private BigDecimal balance;

    /**
     * 余额宝余额
     */
    @Excel(name = "余额宝余额")
    private BigDecimal deposit;

    /**
     * 余额宝利息
     */
    @Excel(name = "余额宝利息")
    private BigDecimal depositInterest;

    private String email;

    /**
     * 当前做单数量
     */
    @Excel(name = "当前做单数量")
    private Long currentOrderCount;

    /**
     * 总做单数量
     */
    @Excel(name = "总做单数量")
    private Long totalOrderCount;

    /**
     * 冻结金额
     */
    @Excel(name = "冻结金额")
    private BigDecimal frozenAmount;

    /**
     * 充值金额
     */
    @Excel(name = "充值金额")
    private BigDecimal recharge;

    /**
     * 充值次数
     */
    @Excel(name = "充值次数")
    private Long rechargeCount;

    /**
     * 提现金额
     */
    @Excel(name = "提现金额")
    private BigDecimal draw;

    /**
     * 提现次数
     */
    @Excel(name = "提现次数")
    private Long drawCount;

    /**
     * 充值待审核金额
     */
    @Excel(name = "充值待审核金额")
    private BigDecimal auditingRecharge;

    /**
     * 提现待审核金额
     */
    @Excel(name = "提现待审核金额")
    private BigDecimal auditingDraw;

    /**
     * 佣金
     */
    @Excel(name = "佣金")
    private BigDecimal commission;

    /**
     * 一级代理人数
     */
    @Excel(name = "一级代理人数")
    private Long oneCount;


    /**
     * 货币
     */
    private String currency;

    /**
     * 当日做单数量
     */
    private Long dayOrderCount = 0L;


    /**
     * 用户积分
     */
    private Long userPoints = 0L;

    /**
     * 历史文本
     */
    private String historyText;

    /**
     * 允许提现总金额
     */
    private BigDecimal allowedWithdrawalTotalAmount;

    private String shopName;

    private Integer shopRate = 3;

    /**
     * 代充金额
     */
    private BigDecimal samuraiAmount;

    private Boolean editShopName = true;


    private Long orderCount;

    /** 2->女；1->男 */
    private String gender;
}
