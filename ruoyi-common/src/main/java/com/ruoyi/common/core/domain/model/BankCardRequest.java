package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author：美式续命
 * @date: 2024/6/29
 * @Copyright：
 */
@Data
public class BankCardRequest {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 开户人
     */
    @Excel(name = "开户人", width = 15)
    @ApiModelProperty(value = "开户人")
    private String accountHolder;
    /**
     * 开户银行
     */
    @Excel(name = "开户银行", width = 15)
    @ApiModelProperty(value = "开户银行")
    private String accountBank;
    /**
     * 银行代码
     */
    @Excel(name = "银行代码", width = 15)
    @ApiModelProperty(value = "银行代码")
    private String bankCode;
    /**
     * 银行卡号
     */
    @Excel(name = "银行卡号", width = 15)
    @ApiModelProperty(value = "银行卡号")
    private String bankCardNumber;

    /**
     * 提现密码
     */
    @Excel(name = "提现密码", width = 15)
    @ApiModelProperty(value = "提现密码")
    private String withdrawPassword;

    @ApiModelProperty(value = "upiId")
    private String upiId;

    /** 提现方式 1->银行卡 2->wallet */
    private Integer withdrawType;

    /** 钱包类型 1->Bkash 2->Nagad 3->Rocket */
    private Integer walletType;

    private String routingNumber;
}
