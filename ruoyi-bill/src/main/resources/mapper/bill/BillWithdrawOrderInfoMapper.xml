<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillWithdrawOrderInfoMapper">

    <resultMap type="com.ruoyi.bill.domain.BillWithdrawOrderInfo" id="BillWithdrawOrderInfoResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="customId"    column="custom_id"    />
        <result property="withdrawAmount"    column="withdraw_amount"    />
        <result property="withdrawFee"    column="withdraw_fee"    />
        <result property="balance"    column="balance"    />
        <result property="extra"    column="extra"    />
        <result property="payoutMethod"    column="payout_method"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="reason"    column="reason"    />
        <result property="dealTime"    column="deal_time"    />
        <result property="accountHolder"    column="account_holder"    />
        <result property="accountBank"    column="account_bank"    />
        <result property="bankCode"    column="bank_code"    />
        <result property="bankCardNumber"    column="bank_card_number"    />
        <result property="withdrawPassword"    column="withdraw_password"    />
        <result property="upiId"    column="upi_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
