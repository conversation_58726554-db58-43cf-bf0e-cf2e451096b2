<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillCustomAmountMapper">

    <update id="subtractBalance" parameterType="com.ruoyi.bill.domain.BillCustomAmount">
        <selectKey resultType="java.math.BigDecimal" keyColumn="balance" keyProperty="plusModel.afterBalance"
                   order="AFTER">
            SELECT m.balance FROM bill_custom_amount m WHERE m.custom_id = #{plusModel.customId}
        </selectKey>
        update bill_custom_amount m
        <set>
            <if test="plusModel.balance != null">m.balance = IFNULL(m.balance,0) - #{plusModel.balance},</if>
        </set>
        where m.custom_id = #{plusModel.customId} and balance >= #{plusModel.balance}
    </update>

    <update id="addBalance" parameterType="com.ruoyi.bill.domain.BillCustomAmount">
        <selectKey resultType="java.math.BigDecimal" keyColumn="balance" keyProperty="plusModel.afterBalance"
                   order="AFTER">
            SELECT m.balance FROM bill_custom_amount m WHERE m.custom_id = #{plusModel.customId}
        </selectKey>
        update bill_custom_amount m
        <set>
            <if test="plusModel.balance != null">m.balance = IFNULL(m.balance,0) + #{plusModel.balance},</if>
        </set>
        where m.custom_id = #{plusModel.customId}
    </update>

    <update id="plusDepositBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE bill_custom_amount
            <set>
                <if test="item.deposit != null">deposit = IFNULL(deposit,0) + #{item.deposit},</if>
                <if test="item.depositInterest != null">deposit_interest = IFNULL(deposit_interest,0) + #{item.depositInterest},</if>
                <if test="item.settleTime != null">settle_time = #{item.settleTime},</if>
            </set>
            where custom_id = #{item.customId}
        </foreach>
    </update>

    <update id="plusDeposit" parameterType="com.ruoyi.bill.domain.BillCustomAmount">
        update bill_custom_amount m
        <set>
            <if test="plusModel.deposit != null">m.deposit = IFNULL(m.deposit,0) + #{plusModel.deposit},</if>
            <if test="plusModel.rollinTime != null">m.rollin_time = #{plusModel.rollinTime},</if>
            <if test="plusModel.rolloutTime != null">m.rollout_time = #{plusModel.rolloutTime},</if>
        </set>
        where m.custom_id = #{plusModel.customId}
    </update>

    <update id="samurai" >
        <selectKey resultType="java.math.BigDecimal" keyColumn="samurai_amount" keyProperty="samuraiAmount"
                   order="AFTER">
            SELECT m.samurai_amount FROM bill_custom_amount m WHERE m.custom_id = #{customId}
        </selectKey>
        update bill_custom_amount m
        <set>
            <if test="samuraiAmount != null">m.samurai_amount = IFNULL(m.samurai_amount,0) + #{samuraiAmount},</if>
        </set>
        where m.custom_id = #{customId}
    </update>


    <update id="returnSamurai">
        <selectKey resultType="java.math.BigDecimal" keyColumn="samurai_amount" keyProperty="samuraiAmount"
                   order="AFTER">
            SELECT m.samurai_amount FROM bill_custom_amount m WHERE m.custom_id = #{customId}
        </selectKey>
        update bill_custom_amount m
        <set>
            <if test="samuraiAmount != null">m.samurai_amount = IFNULL(m.samurai_amount,0) - #{samuraiAmount},</if>
        </set>
        where m.custom_id = #{customId}
    </update>

</mapper>
