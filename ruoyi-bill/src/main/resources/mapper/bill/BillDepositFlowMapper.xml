<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillDepositFlowMapper">

    <resultMap type="com.ruoyi.bill.domain.BillDepositFlow" id="BillDepositFlowResult">
        <result property="depositFlowId"    column="deposit_flow_id"    />
        <result property="flowNo"    column="flow_no"    />
        <result property="customId"    column="custom_id"    />
    </resultMap>

    <select id="selectBillDepositFlowPage" parameterType="com.ruoyi.bill.domain.condition.BillDepositFlowCondition" resultType="com.ruoyi.bill.domain.BillDepositFlow">
        SELECT
        f.deposit_flow_id, f.flow_no, f.amount, f.source, f.out_status, f.custom_id, f.create_time, f.`before`, f.`after`,
        c.username, c.phone_code, c.phone
        FROM
        bill_deposit_flow f
        LEFT JOIN bill_custom c ON f.custom_id = c.custom_id
        <where>
            <if test="condition.phone != null and condition.phone != ''">
                AND c.phone = #{condition.phone}
            </if>
            <if test="condition.username != null and condition.username != ''">
                AND c.username = #{condition.username}
            </if>
            <if test="condition.outStatus != null and condition.outStatus != ''">
                AND f.out_status = #{condition.outStatus}
            </if>
            <if test="condition.source != null and condition.source != ''">
                AND f.source = #{condition.source}
            </if>
            <if test="condition.customId != null">
                AND f.custom_id = #{condition.customId}
            </if>
        </where>
        order by f.deposit_flow_id desc
    </select>
</mapper>
