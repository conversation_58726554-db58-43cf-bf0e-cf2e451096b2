<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillGoodsOrderInfoMapper">

    <resultMap type="com.ruoyi.bill.domain.BillGoodsOrderInfo" id="BillGoodsOrderInfoResult">
        <result property="id"    column="id"    />
        <result property="customId"    column="custom_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsLogo"    column="goods_logo"    />
        <result property="goodsName"    column="goods_name"    />
        <result property="shopName"    column="shop_name"    />
        <result property="goodsNum"    column="goods_num"    />
        <result property="goodsPrice"    column="goods_price"    />
        <result property="goodsTotalPrice"    column="goods_total_price"    />
        <result property="balance"    column="balance"    />
        <result property="exchangeAmount"    column="exchange_amount"    />
        <result property="exchangeBalanceAmount"    column="exchange_balance_amount"    />
        <result property="exchangeGoldAmount"    column="exchange_gold_amount"    />
        <result property="frozenAmount"    column="frozen_amount"    />
        <result property="commission"    column="commission"    />
        <result property="doOrderLevel"    column="do_order_level"    />
        <result property="payTime"    column="pay_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderType"    column="order_type"    />
        <result property="content"    column="content"    />
        <result property="levelId"    column="level_id"    />
        <result property="injectionId"    column="injection_id"    />
        <result property="commissionMultiple"    column="commission_multiple"    />
        <result property="goldOrderId"    column="gold_order_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
