<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillCustomMapper">


    <select id="selectCustomPage" parameterType="com.ruoyi.bill.domain.condition.BillCustomCondition" resultType="com.ruoyi.common.core.domain.dto.BillCustomDto">
        SELECT t.custom_id, t.username, t.invite_code, t.email,t.phone_code, t.phone, t.avatar, t.level,
        t.password_text, t.parent_id, t.parent_username,
        t.status, t.online, t.type, t.register_time, t.register_ip, t.login_time, t.login_ip,
        t.service_team_id,t.can_draw, t.score,t.remark,t.range_min,t.range_max,t.history_text,t.allowed_withdrawal_total_amount,
        t.gender,
        d.balance, d.deposit, d.deposit_interest, d.current_order_count, d.total_order_count, d.frozen_amount,
        d.recharge, d.recharge_count, d.draw, d.draw_count, d.auditing_recharge, d.auditing_draw, d.commission,
        d.one_count, v.name as vip_name,g.group_name,d.user_points,d.samurai_amount,
        h.shop_name,h.shop_rate
        FROM bill_custom t
        left join bill_custom_amount d on t.custom_id = d.custom_id
        left join bill_vip v on t.level = v.level
        left join bill_support_group g on t.service_team_id = g.support_group_id
        left join bill_seller_shop h on t.custom_id = h.custom_id
        <where>
            <if test="condition.registerIp != null and condition.registerIp != ''">
                AND t.register_ip = #{condition.registerIp}
            </if>
            <if test="condition.online != null and condition.online != ''">
                AND t.online = #{condition.online}
            </if>
            <if test="condition.phone != null and condition.phone != ''">
                AND t.phone like concat('%', #{condition.phone}, '%')
            </if>
            <if test="condition.email != null and condition.email != ''">
                AND t.email = #{condition.email}
            </if>
            <if test="condition.type != null and condition.type != ''">
                AND t.type = #{condition.type}
            </if>
            <if test="condition.status != null and condition.status != ''">
                AND t.status = #{condition.status}
            </if>
            <if test="condition.level != null">
                AND t.level = #{condition.level}
            </if>
            <if test="condition.loginTimeList != null">
                AND t.login_time &gt;= #{condition.loginTimeList[0]} AND t.login_time &lt;= #{condition.loginTimeList[1]}
            </if>
            <if test="condition.registerTimeList != null">
                AND t.register_time &gt;= #{condition.registerTimeList[0]} AND t.register_time &lt;= #{condition.registerTimeList[1]}
            </if>
            <if test="condition.parentId != null">
                AND t.parent_id = #{condition.parentId}
            </if>
            <if test="condition.inviteCode != null and condition.inviteCode != ''">
                AND t.invite_code = #{condition.inviteCode}
            </if>
            <if test="condition.username != null and condition.username != ''">
                AND t.username like concat( #{condition.username}, '%')
            </if>
            <if test="condition.customId != null">
                AND t.custom_id = #{condition.customId}
            </if>
            <if test="condition.serviceTeamId != null">
                AND t.service_team_id = #{condition.serviceTeamId}
            </if>
            <if test="condition.gender != null">
                AND t.gender = #{condition.gender}
            </if>

        </where>
        order by t.custom_id desc
    </select>


    <select id="selectExportData"  resultType="com.ruoyi.common.core.domain.dto.ExportBillCustom">


        SELECT
            subquery.custom_id,
            subquery.username,
            subquery.phone,
            subquery.level,
            subquery.login_time,
            subquery.account_bank,
            subquery.bank_code,
            subquery.bank_card_number,
            subquery.withdraw_amount,
            subquery.recharge_amount
        FROM (
                 SELECT custom.custom_id,
                        custom.`username`,
                        custom.`level`,
                        custom.`phone`,
                        custom.`login_time`,
                        bank.`account_bank`,
                        bank.`bank_code`,
                        bank.`bank_card_number`,
                        withdraw.`withdraw_amount`,
                        recharge.recharge_amount,
                        ROW_NUMBER() OVER (PARTITION BY custom.custom_id ORDER BY custom.custom_id) as rn
                 FROM bill_custom custom
                          INNER JOIN bill_member_bank_info bank ON custom.custom_id = bank.custom_id
                          INNER JOIN bill_withdraw_order_info withdraw ON custom.custom_id = withdraw.custom_id
                          INNER JOIN bill_recharge_order_info recharge ON custom.custom_id = recharge.custom_id
                 WHERE recharge.order_status = 1
                   AND withdraw.`order_status` = 1
             ) subquery
        WHERE
            subquery.rn = 1;
    </select>


    <select id="selectSellerShopNum" resultType="com.ruoyi.common.core.domain.dto.ShopDto">
        select custom_id, shop_name,shop_rate
        from bill_seller_shop
        where custom_id = #{customId}
    </select>


    <update id="updateShopName">
        update bill_seller_shop
        set shop_name = #{shopName}
        <if test="shopRate != null">
            ,shop_rate = #{shopRate}
        </if>
        where custom_id = #{customId}
    </update>

    <insert id="insertShopName">
        insert into bill_seller_shop(custom_id, shop_name,shop_rate)
        values (#{customId}, #{shopName},#{shopRate})
    </insert>

</mapper>
