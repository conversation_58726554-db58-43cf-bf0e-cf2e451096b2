<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bill.mapper.BillPurchaseGoodsRecordMapper">

    <resultMap type="com.ruoyi.bill.domain.BillPurchaseGoodsRecord" id="BillPurchaseGoodsRecordResult">
        <result property="purchaseGoodsRecordId"    column="purchase_goods_record_id"    />
        <result property="purchaseGoodsId"    column="purchase_goods_id"    />
        <result property="customId"    column="custom_id"    />
        <result property="name"    column="name"    />
        <result property="days"    column="days"    />
        <result property="goodsPrice"    column="goods_price"    />
        <result property="recordStatus"    column="record_status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

</mapper>
