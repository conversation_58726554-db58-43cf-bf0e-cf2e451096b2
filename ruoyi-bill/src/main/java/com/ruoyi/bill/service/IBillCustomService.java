package com.ruoyi.bill.service;

import com.ruoyi.bill.domain.condition.BillCustomCondition;
import com.ruoyi.common.core.domain.dto.BillCustomDto;
import com.ruoyi.common.core.domain.dto.ExportBillCustom;
import com.ruoyi.common.core.domain.dto.GoogleAuthInfo;
import com.ruoyi.common.core.domain.dto.ShopDto;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.LoginCustom;
import com.ruoyi.common.core.page.Pagination;
import com.ruoyi.common.core.service.SimpleService;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 会员信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
public interface IBillCustomService extends SimpleService<BillCustom, BillCustomCondition>
{

    /**
     * 查询客户
     * @param condition
     * @return
     */
    public Pagination<BillCustomDto> selectCustomPage(BillCustomCondition condition);
    /**
     * 根据账号查询
     * @param username
     * @return
     */
    BillCustom selectCustomByUsername(String username);

    /**
     * 根据customId查询
     * @param customId
     * @return
     */
    BillCustom selectCustomByCustomId(Long customId);
    /**
     * 根据账号查询
     * @param phone
     * @return
     */
    BillCustom selectCustomByPhone(String phone, String phoneCode);
    /**
     * 根据邀请码查询
     * @param inviteCode
     * @return
     */
    BillCustom selectCustomByInviteCode(String inviteCode);

    /**
     * 处理邀请绑定关系
     * @param billCustom
     * @param inviteCode
     * @param isAdmin 是否后台
     */
    void handleInviteRelation(BillCustom billCustom, String inviteCode, boolean isAdmin);
    /**
     * 转成BillCustom
     * @param loginCustom
     * @return
     */
    BillCustom convertToBillCustom(LoginCustom loginCustom);
    /**
     * 校验注册参数
     * @param loginCustom
     */
    public void validRegisterParams(LoginCustom loginCustom);

    /**
     * 校验邀请码
     * @param loginCustom
     */
    public void verifyInviteCode(LoginCustom loginCustom);

    /**
     * 校验注册规则
     * @param billCustom
     */
    public void verifyRegisterRule(BillCustom billCustom, String registerType);
    /**
     * 校验账号唯一
     * @param username
     */
    public void checkUsernameUnique(String username);
    /**
     * 校验手机号唯一
     */
    void checkPhoneUnique(String phone, String phoneCode);
    /**
     * 注册
     * @param billCustom
     */
    void register(BillCustom billCustom);

    /**
     * 后台注册账号
     * @param billCustom
     */
    void registerBack(BillCustom billCustom);

    /**
     * 删除账号
     * @param customId 评级
     */
    int deleteCustom(Long customId);

    /**
     * 生成用户名
     * @param billCustom
     * @return
     */
    void generateUsername(BillCustom billCustom);

    List<ExportBillCustom> selectExportData(BillCustomCondition condition);

    void modifyShopName(LoginCustom loginCustom);

     void updateExtra(Long customId, String key, String value);

    void modifyEmail(LoginCustom loginCustom);

    ShopDto selectSellerShopNum(Long customId);

    /**
     * 处理谷歌授权登录
     *
     * @param googleAuthInfo 谷歌授权信息
     * @return 用户信息
     */
    BillCustom handleGoogleAuth(GoogleAuthInfo googleAuthInfo);

    /**
     * 绑定邀请码
     *
     * @param customId 用户ID
     * @param inviteCode 邀请码
     */
    void bindInviteCode(Long customId, String inviteCode);


    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    BillCustom selectCustomByEmail(String email);

    void checkEmailUnique(@NotBlank String email);
}
