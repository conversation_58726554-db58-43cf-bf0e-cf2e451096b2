package com.ruoyi.bill.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bill.constant.BillCustomConstant;
import com.ruoyi.bill.constant.BillVipConstant;
import com.ruoyi.bill.domain.*;
import com.ruoyi.bill.domain.condition.BillCustomCondition;
import com.ruoyi.bill.domain.vo.BillRegisterConfigVo;
import com.ruoyi.bill.mapper.*;
import com.ruoyi.bill.service.*;
import com.ruoyi.common.core.domain.dto.BillCustomDto;
import com.ruoyi.common.core.domain.dto.ExportBillCustom;
import com.ruoyi.common.core.domain.dto.GoogleAuthInfo;
import com.ruoyi.common.core.domain.dto.ShopDto;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.LoginCustom;
import com.ruoyi.common.core.page.Pagination;
import com.ruoyi.common.core.service.impl.SimpleServiceImpl;
import com.ruoyi.common.enums.SysConfigEnum;
import com.ruoyi.common.exception.BusinessErrorCode;
import com.ruoyi.common.exception.BusinessException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SysConfigUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 会员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Service
public class BillCustomServiceImpl extends SimpleServiceImpl<BillCustomMapper, BillCustom, BillCustomCondition> implements IBillCustomService {
    @Autowired
    private BillCustomMapper billCustomMapper;
    @Autowired
    private BillCustomAmountMapper billCustomAmountMapper;
    @Autowired
    private IBillVipService billVipService;

    @Autowired
    private IBillAsyncService billAsyncService;

    @Autowired
    private BillGoodsOrderInfoMapper billGoodsOrderInfoMapper;

    @Autowired
    private BillWithdrawOrderInfoMapper billWithdrawOrderInfoMapper;

    @Autowired
    private BillBonusHistoryMapper billBonusHistoryMapper;

    @Autowired
    private BillRechargeOrderInfoMapper billRechargeOrderInfoMapper;

    @Autowired
    private BillDepositFlowMapper billDepositFlowMapper;

    @Autowired
    private BillFlowMapper billFlowMapper;

    @Autowired
    private BillMemberBankInfoMapper billMemberBankInfoMapper;

    @Autowired
    private BillMemberInjectionInfoMapper billMemberInjectionInfoMapper;

    @Autowired
    private BillMemberSignInfoMapper billMemberSignInfoMapper;



    @Override
    public void validRegisterParams(LoginCustom loginCustom) {

    }

    @Override
    public void verifyInviteCode(LoginCustom loginCustom) {
        if (StringUtils.isBlank(loginCustom.getInviteCode())) {
            return;
        }
        BillCustom billCustom = selectCustomByInviteCode(loginCustom.getInviteCode());
        if (billCustom == null) {
            throw new BusinessException("custom.register.inviteCodeInvalid");
        }
        BillVip billVip = billVipService.getVipListByLevel(billCustom.getLevel());
        if (billVip == null) {
            throw new BusinessException("custom.register.allowInviteFalse");
        }
        if (!BillVipConstant.ALLOW_INVITE_1.equals(billVip.getAllowInvite())) {
            throw new BusinessException("custom.register.allowInviteFalse");
        }

    }

    @Override
    public void verifyRegisterRule(BillCustom billCustom, String registerType) {
        BillRegisterConfigVo registerConfigVo = SysConfigUtils.getConfig(SysConfigEnum.BILL_REGISTER_CONFIG, BillRegisterConfigVo.class);
        if (registerConfigVo == null) {
            return;
        }
        boolean phone = registerConfigVo.getPhone() != null && registerConfigVo.getPhone();
        boolean username = registerConfigVo.getUsername() != null && registerConfigVo.getUsername();

        if (BillCustomConstant.REGISTER_PHONE.equals(registerType) && !phone) {
            throw new BusinessException("custom.register.phoneNotAllow");
        }
        if (BillCustomConstant.REGISTER_USERNAME.equals(registerType) && !username) {
            throw new BusinessException("custom.register.usernameNotAllow");
        }

        if (registerConfigVo.getRegisterCount() <= 0) {
            return;
        }
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillCustom::getRegisterIp, billCustom.getRegisterIp());
        Long count = billCustomMapper.selectCount(queryWrapper);
        if (count >= registerConfigVo.getRegisterCount()) {
            throw new BusinessException("custom.register.registerCountMax");
        }

    }

    @Override
    public void checkUsernameUnique(String username) {
        BillCustom billCustom = selectCustomByUsername(username);
        if (Objects.nonNull(billCustom)) {
            throw new BusinessException("custom.register.usernameExist", username);
        }
    }

    @Override
    public void checkPhoneUnique(String phone, String phoneCode) {
        BillCustom billCustom = selectCustomByPhone(phone, phoneCode);
        if (StringUtils.isNotNull(billCustom)) {
            throw new BusinessException("custom.register.phoneExist", phoneCode + phone);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void register(BillCustom billCustom) {
        billCustom.setPasswordText(billCustom.getPassword());
        billCustom.setPassword(SecurityUtils.encryptPassword(billCustom.getPassword()));
        billCustom.setInviteCode(generateInviteCode());
        if (billCustom.getLevel() == null) {
            billCustom.setLevel(0L);
        }
        billCustom.setRegisterTime(System.currentTimeMillis());
        billCustom.setOnline(BillCustomConstant.ONLINE_0);
        billCustom.setStatus(BillCustomConstant.STATUS_1);
        billCustom.setCanDraw(BillCustomConstant.CAN_DRAW_0);
        if (billCustom.getType() == null) {
            billCustom.setType(BillCustomConstant.TYPE_1);
        }
        billCustom.setScore(80L);
        billCustomMapper.insert(billCustom);

        BillCustomAmount billCustomAmount = new BillCustomAmount();
        billCustomAmount.setCustomId(billCustom.getCustomId());
        billCustomAmount.setUsername(billCustom.getUsername());
        billCustomAmount.setBalance(BigDecimal.ZERO);
        billCustomAmount.setDeposit(BigDecimal.ZERO);
        billCustomAmount.setDepositInterest(BigDecimal.ZERO);
        billCustomAmount.setOneCount(0L);
        billCustomAmountMapper.insert(billCustomAmount);

        if (billCustom.getParentId() != null) {
            //处理邀请后续业务
            billAsyncService.handleInviteBusiness(billCustom.getParentId());
        }

    }

    @Override
    public void registerBack(BillCustom billCustom) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCustom(Long customId) {
        int result = billCustomMapper.deleteById(customId);

        billCustomAmountMapper.deleteById(customId);

        LambdaQueryWrapper<BillGoodsOrderInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillGoodsOrderInfo::getCustomId, customId);
        billGoodsOrderInfoMapper.delete(queryWrapper);

        LambdaQueryWrapper<BillWithdrawOrderInfo> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(BillWithdrawOrderInfo::getCustomId, customId);
        billWithdrawOrderInfoMapper.delete(queryWrapper1);

        LambdaQueryWrapper<BillBonusHistory> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(BillBonusHistory::getCustomId, customId);
        billBonusHistoryMapper.delete(queryWrapper2);

        LambdaQueryWrapper<BillRechargeOrderInfo> queryWrapper3 = new LambdaQueryWrapper<>();
        queryWrapper3.eq(BillRechargeOrderInfo::getCustomId, customId);
        billRechargeOrderInfoMapper.delete(queryWrapper3);

        LambdaQueryWrapper<BillDepositFlow> queryWrapper4 = new LambdaQueryWrapper<>();
        queryWrapper4.eq(BillDepositFlow::getCustomId, customId);
        billDepositFlowMapper.delete(queryWrapper4);

        LambdaQueryWrapper<BillFlow> queryWrapper5 = new LambdaQueryWrapper<>();
        queryWrapper5.eq(BillFlow::getCustomId, customId);
        billFlowMapper.delete(queryWrapper5);

        LambdaQueryWrapper<BillMemberBankInfo> queryWrapper6 = new LambdaQueryWrapper<>();
        queryWrapper6.eq(BillMemberBankInfo::getCustomId, customId);
        billMemberBankInfoMapper.delete(queryWrapper6);

        LambdaQueryWrapper<BillMemberInjectionInfo> queryWrapper7 = new LambdaQueryWrapper<>();
        queryWrapper7.eq(BillMemberInjectionInfo::getCustomId, customId);
        billMemberInjectionInfoMapper.delete(queryWrapper7);

        LambdaQueryWrapper<BillMemberSignInfo> queryWrapper8 = new LambdaQueryWrapper<>();
        queryWrapper8.eq(BillMemberSignInfo::getCustomId, customId);
        billMemberSignInfoMapper.delete(queryWrapper8);

        return result;
    }

    @Override
    public void generateUsername(BillCustom billCustom) {
        String phoneLast = org.apache.commons.lang3.StringUtils.right(billCustom.getPhone(), 4);
        String username = generateUniqueUsername(phoneLast,billCustom);
        billCustom.setUsername(username);
    }

    @Override
    public Pagination<BillCustomDto> selectCustomPage(BillCustomCondition condition) {
        Page page = extractPageTurnParam(condition);
        IPage<BillCustomDto> result = billCustomMapper.selectCustomPage(page, condition);
        return super.convertObjectResultToPagination(result, page);
    }

    @Override
    public BillCustom selectCustomByUsername(String username) {
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BillCustom::getCustomId, BillCustom::getUsername, BillCustom::getPassword,
                BillCustom::getPasswordText, BillCustom::getParentId, BillCustom::getStatus,BillCustom::getLevel);
        queryWrapper.eq(BillCustom::getUsername, username);
        return billCustomMapper.selectOne(queryWrapper);
    }

    @Override
    public BillCustom selectCustomByCustomId(Long customId) {
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BillCustom::getCustomId, BillCustom::getUsername, BillCustom::getParentId, BillCustom::getStatus, BillCustom::getCanDraw, BillCustom::getLevel, BillCustom::getType, BillCustom::getScore);
        queryWrapper.eq(BillCustom::getCustomId, customId);
        return billCustomMapper.selectOne(queryWrapper);
    }

    @Override
    public BillCustom selectCustomByPhone(String phone, String phoneCode) {
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BillCustom::getCustomId, BillCustom::getUsername, BillCustom::getPhoneCode, BillCustom::getPhone, BillCustom::getPassword, BillCustom::getParentId, BillCustom::getStatus,BillCustom::getLevel);
        queryWrapper.eq(BillCustom::getPhoneCode, phoneCode);
        queryWrapper.eq(BillCustom::getPhone, phone);
        return billCustomMapper.selectOne(queryWrapper);
    }

    @Override
    public BillCustom selectCustomByInviteCode(String inviteCode) {
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BillCustom::getCustomId, BillCustom::getUsername, BillCustom::getPassword, BillCustom::getParentId, BillCustom::getStatus,BillCustom::getLevel,BillCustom::getServiceTeamId);
        queryWrapper.eq(BillCustom::getInviteCode, inviteCode);
        return billCustomMapper.selectOne(queryWrapper);
    }

    @Override
    public void handleInviteRelation(BillCustom billCustom, String inviteCode, boolean isAdmin) {
        if (isAdmin && StringUtils.isBlank(inviteCode)) {
            return;
        }
        if (!isAdmin && StringUtils.isBlank(inviteCode)) {
            throw new BusinessException("custom.register.inviteCodeInvalid");
        }
        BillCustom parentCustom = selectCustomByInviteCode(inviteCode);
        if (parentCustom == null) {
            throw new BusinessException("custom.register.inviteCodeInvalid");
        }
        BillVip billVip = billVipService.getVipListByLevel(parentCustom.getLevel());
        if (!BillVipConstant.ALLOW_INVITE_1.equals(billVip.getAllowInvite())) {
            throw new BusinessException("custom.register.allowInviteFalse");
        }
        billCustom.setParentId(parentCustom.getCustomId());
        billCustom.setParentUsername(parentCustom.getUsername());
        // 上面传了分组id就用传入的
        if (Objects.isNull(billCustom.getServiceTeamId())) {
            billCustom.setServiceTeamId(parentCustom.getServiceTeamId());
        }
    }

    @Override
    public BillCustom convertToBillCustom(LoginCustom loginCustom) {
        BillCustom billCustom = new BillCustom();
        billCustom.setPhoneCode(loginCustom.getPhoneCode());
        billCustom.setPhone(loginCustom.getPhone());
        billCustom.setUsername(loginCustom.getUsername());
        billCustom.setPassword(loginCustom.getPassword());
        billCustom.setRegisterIp(loginCustom.getIpaddr());
        billCustom.setEmail(loginCustom.getEmail());
        billCustom.setGender(loginCustom.getGender());
        billCustom.setWithdrawPassword(loginCustom.getPassword());

        handleInviteRelation(billCustom, loginCustom.getInviteCode(), false);
        return billCustom;
    }

    public String generateInviteCode() {
        while (true) {
            String inviteCode = StringUtils.randomString(6);
            BillCustom billCustom = selectCustomByInviteCode(inviteCode);
            if (billCustom == null) {
                return inviteCode;
            }
        }
    }

    public String generateUniqueUsername(String phoneLast, BillCustom billCustom) {
        while (true) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(billCustom.getUsername())) {
                BillCustom billCustom2 = selectCustomByUsername(billCustom.getUsername());
                if (Objects.nonNull(billCustom2)) {
                    throw new BusinessException("custom.register.username.Exist");
                }
                return billCustom.getUsername();
            }
            String username = phoneLast + StringUtils.randomString(6);
            BillCustom billCustom2 = selectCustomByUsername(username);
            if (billCustom2 == null) {
                return username;
            }
        }
    }

    @Override
    public List<ExportBillCustom> selectExportData(BillCustomCondition condition) {
        return billCustomMapper.selectExportData();
    }

    @Override
    public void modifyShopName(LoginCustom loginCustom) {
        Long customId = loginCustom.getCustomId() == null ? SecurityUtils.getCustomId() : loginCustom.getCustomId();
        BillCustom custom = billCustomMapper.selectById(customId);
        String modifyUsernameTime = custom.queryExtra(BillCustomConstant.modifyUsername);
        if (StringUtils.isNotEmpty(modifyUsernameTime) && loginCustom.getCustomId() == null) {
            long l = Long.parseLong(modifyUsernameTime);
            if (l - System.currentTimeMillis() < 365L * 24 * 60 * 60 * 1000) {
                throw new BusinessException("custom.modifyUsername.time.limit");
            }
        }
        ShopDto shopDto = billCustomMapper.selectSellerShopNum(customId);
        if (shopDto != null) {
            billCustomMapper.updateShopName(customId, loginCustom.getShopName(), loginCustom.getShopRate());
        } else {
            billCustomMapper.insertShopName(customId, loginCustom.getShopName(), loginCustom.getShopRate() == null ? 3 : loginCustom.getShopRate());
        }
        updateExtra(customId, BillCustomConstant.modifyUsername, String.valueOf(System.currentTimeMillis()));
    }


    @Override
    public void modifyEmail(LoginCustom loginCustom) {
        Long customId = SecurityUtils.getCustomId();
        billCustomMapper.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getEmail, loginCustom.getEmail())
                .eq(BillCustom::getCustomId, customId)
        );
    }

    @Override
    public void updateExtra(Long customId, String key, String value) {
        ;
        BillCustom custom = billCustomMapper.selectById(customId);
        String extra = custom.getExtra();
        Map<Object, Object> extraMap;
        if (StringUtils.isNotBlank(extra)) {
            extraMap = JSON.parseObject(extra, Map.class);
            if (MapUtils.isEmpty(extraMap)) {
                extraMap = new HashMap<>();
            }
        } else {
            extraMap = new HashMap<>();
        }
        extraMap.put(key, value);
        baseMapper.update(new LambdaUpdateWrapper<>(BillCustom.class)
                .set(BillCustom::getExtra, JSON.toJSONString(extraMap))
                .eq(BillCustom::getCustomId, customId));
    }

    @Override
    public ShopDto selectSellerShopNum(Long customId) {
        return baseMapper.selectSellerShopNum(customId);
    }

    /**
     * 处理谷歌授权登录
     *
     * @param googleAuthInfo 谷歌授权信息
     * @return 用户信息
     */
    @Override
    @Transactional
    public BillCustom handleGoogleAuth(GoogleAuthInfo googleAuthInfo) {
        // 查询是否已存在该谷歌账号关联的用户
        BillCustom existingCustom = selectCustomByGoogleEmail(googleAuthInfo.getEmail());
        if (existingCustom == null) {
            existingCustom = new BillCustom();
            existingCustom.setEmail(googleAuthInfo.getEmail());
            existingCustom.setUsername(googleAuthInfo.getName());
            existingCustom.setStatus(BillCustomConstant.STATUS_1);
            String password = RandomUtil.randomString(10);
            existingCustom.setPassword(password);
            existingCustom.setPasswordText(password);
            existingCustom.setGoogleId(googleAuthInfo.getGoogleId());
            register(existingCustom);
        }
        return existingCustom;
    }

    /**
     * 根据谷歌ID查询用户
     *
     * @param googleEmail 谷歌ID
     * @return 用户信息
     */
    private BillCustom selectCustomByGoogleEmail(String googleEmail) {
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillCustom::getEmail, googleEmail);
        return billCustomMapper.selectOne(queryWrapper);
    }

    /**
     * 绑定邀请码
     *
     * @param customId 用户ID
     * @param inviteCode 邀请码
     */
    @Override
    @Transactional
    public void bindInviteCode(Long customId, String inviteCode) {
        // 查询当前用户
        BillCustom currentCustom = getById(customId);
        if (currentCustom == null) {
            throw new BusinessException("custom.register.userNotExist");
        }

        // 检查用户是否已绑定邀请码
        if (currentCustom.getParentId() != null) {
            throw new BusinessException("custom.register.alreadyBoundInviteCode");
        }

        // 验证邀请码
        BillCustom parentCustom = selectCustomByInviteCode(inviteCode);
        if (parentCustom == null) {
            throw new BusinessException("custom.register.inviteCodeInvalid");
        }

        // 不能绑定自己的邀请码
        if (customId.equals(parentCustom.getCustomId())) {
            throw new BusinessException("custom.register.cannotBindSelf");
        }

        // 检查邀请人是否有邀请权限
        BillVip billVip = billVipService.getVipListByLevel(parentCustom.getLevel());
        if (billVip == null || !BillVipConstant.ALLOW_INVITE_1.equals(billVip.getAllowInvite())) {
            throw new BusinessException("custom.register.allowInviteFalse");
        }

        // 更新用户的邀请关系
        BillCustom updateCustom = new BillCustom();
        updateCustom.setCustomId(customId);
        updateCustom.setParentId(parentCustom.getCustomId());
        updateCustom.setParentUsername(parentCustom.getUsername());
        updateCustom.setServiceTeamId(parentCustom.getServiceTeamId());

        billCustomMapper.updateById(updateCustom);

        // 处理邀请后续业务
        billAsyncService.handleInviteBusiness(parentCustom.getCustomId());
    }

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Override
    public BillCustom selectCustomByEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return null;
        }
        LambdaQueryWrapper<BillCustom> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillCustom::getEmail, email);
        return billCustomMapper.selectOne(queryWrapper);
    }

    @Override
    public void checkEmailUnique(String email) {
        BillCustom custom = selectCustomByEmail(email);
        if (Objects.nonNull(custom)) {
            throw new BusinessException("custom.register.emailExist", email);
        }
    }
}
