package com.ruoyi.bill.service.impl;

import cn.ucloud.ufile.util.MimeTypeUtil;
import com.ruoyi.bill.config.S3Config;
import com.ruoyi.bill.constant.FileConstants;
import com.ruoyi.bill.domain.BillFile;
import com.ruoyi.bill.domain.condition.BillFileCondition;
import com.ruoyi.bill.domain.dto.FileUploadDto;
import com.ruoyi.bill.mapper.BillFileMapper;
import com.ruoyi.bill.service.IBillFileService;
import com.ruoyi.bill.util.MinioFileUploadUtils;
import com.ruoyi.bill.util.MinioUtils;
import com.ruoyi.bill.util.S3UploadUtils;
import com.ruoyi.common.core.service.impl.SimpleServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 文件上传记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-17
 */
@Service
public class BillFileServiceImpl extends SimpleServiceImpl<BillFileMapper, BillFile, BillFileCondition> implements IBillFileService {
    @Autowired
    private S3Config s3Config;

    @Autowired
    private MinioUtils minioUtils;



    @Override
    public FileUploadDto uploadFile(MultipartFile file, String domain, String customFolder) throws Exception {
        FileUploadDto fileUploadDto = new FileUploadDto();
        //充填参数
        this.fillParams(fileUploadDto, file, customFolder);
        String bucketName = s3Config.getBucketName();
        String mimeType = MimeTypeUtil.getMimeType(file.getName());
        String keyName = s3Config.getFileName() + "/" + fileUploadDto.getFileKey();
        S3UploadUtils.putStream(file.getInputStream(), file.getSize(), mimeType, keyName, bucketName);
        return fileUploadDto;
    }



    @Override
    public String generateFileUrl(String domain, String fileKey) {
        String url = minioUtils.getDirectObjectUrl(fileKey);
        if (StringUtils.isNotBlank(domain) && StringUtils.isNotBlank(url)) {
            url = this.handleUrl(domain, url);
        }
        return url;
    }

    /**
     * 处理url
     *
     * @param domain
     * @param url
     * @return
     */
    public String handleUrl(String domain, String url) {
        if (url.contains(FileConstants.HTTPS)) {
            url = url.replace(FileConstants.HTTPS + minioUtils.getBasisUrl(), domain);
            return url;
        }
        url = url.replace(FileConstants.HTTP + minioUtils.getBasisUrl(), domain);
        return url;
    }


    /**
     * 充填参数
     *
     * @param fileUploadDto
     * @param file
     */
    private void fillParams(FileUploadDto fileUploadDto, MultipartFile file, String customFolder) {
        String fileKey = MinioFileUploadUtils.extractFilename(file);
        if (StringUtils.isNotBlank(customFolder)) {
            fileKey = customFolder + MinioFileUploadUtils.SLASH + fileKey;
        }
        String filePath = MinioFileUploadUtils.SLASH + s3Config.getNginxForward() + MinioFileUploadUtils.SLASH + s3Config.getFileName() + MinioFileUploadUtils.SLASH + fileKey;
        fileUploadDto.setFileSize(String.valueOf(file.getSize()));
        fileUploadDto.setFileKey(fileKey);
        fileUploadDto.setFilePath(filePath);
        fileUploadDto.setFileName(file.getOriginalFilename());
        fileUploadDto.setTarget(MinioFileUploadUtils.dateTime());
    }

    /**
     * 删除文件
     *
     * @param objectName 文件名称
     */
    @Override
    public void removeFile(String objectName) throws Exception {
        objectName = handleFileKey(objectName);
        minioUtils.removeFile(null, objectName);
    }

    /**
     * 批量删除文件
     *
     * @param objectNameList 需要删除的文件列表
     */
    @Override
    public void removeFiles(List<String> objectNameList) {
        this.handleFileKey(objectNameList);
        minioUtils.removeFiles(null, objectNameList);
    }

    private String getPrefix() {
        return MinioFileUploadUtils.SLASH + s3Config.getNginxForward() + MinioFileUploadUtils.SLASH + minioUtils.getBucketName() + MinioFileUploadUtils.SLASH;
    }

    private String handleFileKey(String objectName) {
        if (objectName == null) {
            return null;
        }
        String prefix = getPrefix();
        if (objectName.startsWith(prefix)) {
            objectName = objectName.replace(prefix, com.ruoyi.common.utils.StringUtils.EMPTY);
        }
        return objectName;
    }

    private void handleFileKey(List<String> objectNameList) {
        if (CollectionUtils.isEmpty(objectNameList)) {
            return;
        }
        objectNameList.replaceAll(this::handleFileKey);
    }
}
