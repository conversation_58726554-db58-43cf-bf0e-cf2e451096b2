package com.ruoyi.bill.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.bill.domain.BillCustomAmount;
import com.ruoyi.bill.domain.BillGoodsOrderInfo;
import com.ruoyi.bill.domain.BillVip;
import com.ruoyi.bill.mapper.BillCustomAmountMapper;
import com.ruoyi.bill.mapper.BillCustomMapper;
import com.ruoyi.bill.mapper.BillVipMapper;
import com.ruoyi.bill.service.*;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.BillSystemConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.service.impl.SimpleServiceImpl;
import com.ruoyi.bill.mapper.BillRechargeOrderInfoMapper;
import com.ruoyi.bill.domain.BillRechargeOrderInfo;
import com.ruoyi.bill.domain.condition.BillRechargeOrderInfoCondition;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 充值订单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
@Slf4j
@Service
public class BillRechargeOrderInfoServiceImpl extends SimpleServiceImpl<BillRechargeOrderInfoMapper, BillRechargeOrderInfo, BillRechargeOrderInfoCondition>  implements IBillRechargeOrderInfoService
{
    @Autowired
    private BillRechargeOrderInfoMapper billRechargeOrderInfoMapper;
    @Autowired
    private IBillVipService billVipService;
    @Autowired
    private BillCustomMapper billCustomMapper;
    @Autowired
    private BillCustomAmountMapper billCustomAmountMapper;
    @Autowired
    private IBillSystemConfigService billSystemConfigService;
    @Autowired
    private IBillAsyncService billAsyncService;

    @Autowired
    private IBillGoodsOrderInfoService billGoodsOrderInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recharge(BillRechargeOrderInfo billRechargeOrderInfo) {
        BillCustom billCustom = billCustomMapper.selectById(billRechargeOrderInfo.getCustomId());
        BillSystemConfigRequest conf = billSystemConfigService.getConfig(1L);
        String rechargeToCardLimit = conf.getRechargeToCardLimit();
        String[] split = rechargeToCardLimit.split("\\|");
        String card = split[0];
        String bankName = split[1];
        String accountHolder = split[2];
        billRechargeOrderInfo.setOrderNo("CR" + System.currentTimeMillis());
        billRechargeOrderInfo.setRechargeFee(billRechargeOrderInfo.getRechargeAmount().multiply(new BigDecimal(conf.getRechargeFee())).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        if (billRechargeOrderInfo.getPayMethod() == null) {
            billRechargeOrderInfo.setPayMethod(0L);
        }
        if (billRechargeOrderInfo.getType() == null) {
            billRechargeOrderInfo.setType(0L);
        }
        if (billRechargeOrderInfo.getOrderStatus() == null) {
            billRechargeOrderInfo.setOrderStatus(0L);
        }
        billRechargeOrderInfo.setUsername(billCustom.getUsername());
        billRechargeOrderInfo.setPhone(billCustom.getPhone());
        billRechargeOrderInfo.setAccountHolder(accountHolder);
        billRechargeOrderInfo.setAccountBank(bankName);
        billRechargeOrderInfo.setBankCardNumber(card);
        this.save(billRechargeOrderInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rechargeFollowUp(BillRechargeOrderInfo billRechargeOrderInfo, BillCustom billCustom) {

        //充值vip升级
        handleRechargeLevelUp(billRechargeOrderInfo, billCustom);

        if (billCustom.getParentId() != null) {
            //充值返佣
            billAsyncService.handleRechargeRebate(billRechargeOrderInfo, billCustom.getParentId());
        }
    }

    @Override
    public BigDecimal rechargeCount() {
        return billRechargeOrderInfoMapper.rechargeCount();
    }

    /**
     * 充值vip升级
     * @param billRechargeOrderInfo
     */
    void handleRechargeLevelUp(BillRechargeOrderInfo billRechargeOrderInfo, BillCustom billCustom) {


        BillVip currentVip = billVipService.getVipListByLevel(billCustom.getLevel());
        if (Objects.isNull(currentVip) || currentVip.getPrice() == null || currentVip.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BillCustomAmount billCustomAmount = billCustomAmountMapper.selectById(billRechargeOrderInfo.getCustomId());
        if (billCustomAmount.getRecharge().compareTo(currentVip.getPrice()) < 0) {
            return;
        }
        List<BillVip> billVips = billVipService.getVipListByCache();

        // 会员等级最高了就不要再更新了
        BillVip maxVipLevel = billVips.get(billVips.size() - 1);
        if (Objects.equals(billCustom.getLevel(), maxVipLevel.getLevel())) {
            log.info("客户{}已达最高的等级", billCustom.getUsername());
            return;
        }
        //最终升级的vip等级
        BillVip finalVip = null;

        for (BillVip memberLevel : billVips) {
            if (billCustomAmount.getRecharge().compareTo(memberLevel.getPrice()) >= 0) {
                finalVip = memberLevel;
            }
        }

        if (Objects.isNull(finalVip)) {
            return;
        }

        BillCustom update = new BillCustom();
        update.setCustomId(billCustom.getCustomId());
        update.setLevel(finalVip.getLevel());
        billCustomMapper.updateById(update);
        // 更新一下订单的等级
        billGoodsOrderInfoService.updateOrderLevel(billCustom.getCustomId(), finalVip.getLevel());

    }
}
