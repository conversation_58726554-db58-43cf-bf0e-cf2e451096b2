package com.ruoyi.bill.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "s3")
@Data
public class S3Config {

    private String region;

    private String endpoint;
    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 秘钥
     */
    private String privateKey;

    /**
     * 桶名称
     */
    private String bucketName;

    /**
     * nginx转发路径
     */
    private String nginxForward;

    /**
     * 文件夹名称
     */
    private String fileName;

}
