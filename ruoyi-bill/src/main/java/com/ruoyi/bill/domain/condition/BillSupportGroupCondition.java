package com.ruoyi.bill.domain.condition;

import com.ruoyi.common.core.domain.condition.SimpleCondition;
import com.ruoyi.common.core.wrapper.WhereType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客服组对象 bill_support_group
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel("客服组")
public class BillSupportGroupCondition extends SimpleCondition
{

    @ApiModelProperty("客服分类id")
    @WhereType
    private Long supportGroupId;
    @ApiModelProperty("分类名称")
    @WhereType
    private String groupName;
    @ApiModelProperty("创建时间")
    @WhereType
    private Long createTime;
    @ApiModelProperty("更新时间")
    @WhereType
    private Long updateTime;

    /** 0->非默认;1->默认 */
    @WhereType
    @ApiModelProperty("0->否;1->是")
    private String defaultGroup;

}
