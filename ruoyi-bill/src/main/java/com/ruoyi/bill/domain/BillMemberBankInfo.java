
package com.ruoyi.bill.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.model.SimpleModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 银行卡信息对象 bill_member_bank_info
 *
 * <AUTHOR>
 * @date 2024-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_member_bank_info")
public class BillMemberBankInfo extends SimpleModel
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private String id;

    /** 会员主键id */
    @Excel(name = "会员主键id")
    private Long customId;

    /** 开户人 */
    @Excel(name = "开户人")
    private String accountHolder;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String accountBank;

    /** 银行代码 */
    @Excel(name = "银行代码")
    private String bankCode;

    /** 卡号 */
    @Excel(name = "卡号")
    private String bankCardNumber;

    /** 提现密码 */
    @Excel(name = "提现密码")
    private String withdrawPassword;

    /** upiId */
    @Excel(name = "upiId")
    private String upiId;

    /** 提现方式 1->银行卡 2->wallet */
    private Integer withdrawType;

    /** 钱包类型 1->Bkash 2->Nagad 3->Rocket */
    private Integer walletType;

    private String routingNumber;

    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT) // 插入时自动填充
    private Long createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE) // 插入时自动填充
    /** 更新日期 */
    private Long updateTime;


}
