package com.ruoyi.bill.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.entity.BillCustom;
import com.ruoyi.common.core.domain.model.SimpleModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提现订单信息对象 bill_withdraw_order_info
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_withdraw_order_info")
public class BillWithdrawOrderInfo extends SimpleModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderNo;

    /**
     * 会员主键id
     */
    @Excel(name = "会员主键id")
    private Long customId;

    private String username;
    @Excel(name = "手机号")
    private String phone;

    /**
     * 提现金额
     */
    @Excel(name = "提现金额")
    private BigDecimal withdrawAmount;

    /**
     * 手续费
     */
    @Excel(name = "手续费")
    private BigDecimal withdrawFee;

    /**
     * 余额
     */
    @Excel(name = "余额")
    private BigDecimal balance;

    /**
     * 扩展时间
     */
    @Excel(name = "扩展时间")
    private String extra;

    /**
     * 收款方式0->bank
     */
    @Excel(name = "收款方式0->bank")
    private Integer payoutMethod;

    /**
     * 0->未审核;1->已通过;2->已驳回;3->转账失败
     */
    @Excel(name = "0->未审核;1->已通过;2->已驳回;3->转账失败")
    private Integer orderStatus;

    /**
     * 拒绝原因
     */
    @Excel(name = "拒绝原因")
    private String reason;

    /**
     * 申请日期
     */
    @Excel(name = "申请日期")
    private Long dealTime;

    /**
     * 开户人
     */
    @Excel(name = "开户人")
    private String accountHolder;

    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    private String accountBank;

    /**
     * 银行代码
     */
    @Excel(name = "银行代码")
    private String bankCode;

    /**
     * 卡号
     */
    @Excel(name = "卡号")
    private String bankCardNumber;

    @Excel(name = "提现方式 1->银行卡 2->wallet")
    private Integer withdrawType;

    /** 钱包类型 1->Bkash 2->Nagad 3->Rocket */
    private Integer walletType;

    private String routingNumber;

    /**
     * 提现密码
     */
    @Excel(name = "提现密码")
    private String withdrawPassword;

    /**
     * upiId
     */
    @Excel(name = "upiId")
    private String upiId;

    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT) // 插入时自动填充
    private Long createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE) // 插入时自动填充
    /** 更新日期 */
    private Long updateTime;


    @TableField(exist = false)
    private String password;

    @TableField(exist = false)
    private BillCustom custom;

    @TableField(exist = false)
    private BillCustom parentCustom;

      @TableField(exist = false)
    private Long sameBankCardNumber;

    @TableField(exist = false)
    private Long sameName;

    private Integer level;

}
