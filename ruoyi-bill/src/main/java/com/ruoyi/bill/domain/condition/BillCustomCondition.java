package com.ruoyi.bill.domain.condition;

import com.ruoyi.common.core.domain.condition.SimpleCondition;
import com.ruoyi.common.core.wrapper.WhereType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 会员信息对象 bill_custom
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("会员信息")
public class BillCustomCondition extends SimpleCondition {

    @ApiModelProperty("客户ID")
    @WhereType
    private Long customId;
    @ApiModelProperty("客户账号")
    @WhereType
    private String username;
    @ApiModelProperty("邀请码")
    @WhereType
    private String inviteCode;
    @ApiModelProperty("客户邮箱")
    @WhereType
    private String email;
    @ApiModelProperty("手机号码")
    @WhereType
    private String phone;
    @ApiModelProperty("vip等级")
    @WhereType
    private Long level;
    @ApiModelProperty("上级客户id")
    @WhereType
    private Long parentId;
    @ApiModelProperty("帐号状态（0未激活 1已激活）")
    @WhereType
    private String status;
    @ApiModelProperty("在线状态（0离线 1在线）")
    @WhereType
    private String online;
    @ApiModelProperty("客户类型（1真人 2假人）")
    @WhereType
    private String type;
    @ApiModelProperty("创建时间")
    @WhereType(ignore = true)
    private List<Long> registerTimeList;
    @ApiModelProperty("注册IP")
    @WhereType
    private String registerIp;
    @ApiModelProperty("登入时间")
    @WhereType(ignore = true)
    private List<Long> loginTimeList;
    @ApiModelProperty("登录ip")
    @WhereType
    private String loginIp;
    @ApiModelProperty("客服组id")
    @WhereType
    private Long serviceTeamId;

    @WhereType
    private String password;

    @WhereType
    private String gender;

}
