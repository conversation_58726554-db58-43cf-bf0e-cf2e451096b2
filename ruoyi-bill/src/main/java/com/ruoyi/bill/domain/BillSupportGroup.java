package com.ruoyi.bill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.ruoyi.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.model.SimpleModel;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客服组对象 bill_support_group
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bill_support_group")
public class BillSupportGroup extends SimpleModel
{
    private static final long serialVersionUID = 1L;

    /** 客服分类id */
    @TableId(type = IdType.AUTO)
    private Long supportGroupId;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String groupName;

    /** 创建时间 */
    private Long createTime;

    /** 更新时间 */
    private Long updateTime;

    /** 0->非默认;1->默认 */
    private String defaultGroup;

}
