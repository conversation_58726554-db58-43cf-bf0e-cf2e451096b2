package com.ruoyi.bill.util;

import cn.ucloud.ufile.UfileClient;
import cn.ucloud.ufile.api.object.ObjectConfig;
import cn.ucloud.ufile.auth.UfileObjectLocalAuthorization;
import cn.ucloud.ufile.bean.PutObjectResultBean;
import cn.ucloud.ufile.exception.UfileClientException;
import cn.ucloud.ufile.exception.UfileServerException;
import cn.ucloud.ufile.util.JLog;
import cn.ucloud.ufile.util.StorageType;
import com.ruoyi.bill.config.S3Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;


@Component
public class S3UploadUtils {


    private static S3Config s3Config;

    @Autowired
    public void setMessageService(S3Config s3Config) {
        S3UploadUtils.s3Config = s3Config;
    }

    private static final String TAG = "PutObjectSample";
    private static final ObjectConfig config = new ObjectConfig(s3Config.getRegion(), s3Config.getEndpoint());

    public static void putStream(InputStream stream, long contentLength, String mimeType, String nameAs, String toBucket) {
        try {
            /**
             * 上传回调策略
             * 必须填写回调接口url(目前仅支持http，不支持https)，可选填回调参数，回调参数请自行决定是否需要urlencode
             * 若配置上传回调，则上传接口的回调将会透传回调接口的response，包括httpCode
             */
            PutObjectResultBean response = UfileClient.object(new UfileObjectLocalAuthorization(s3Config.getPublicKey(), s3Config.getPrivateKey()), config)
                    .putObject(stream, contentLength, mimeType)
                    .nameAs(nameAs)
                    .toBucket(toBucket)
                    /**
                     * 配置文件存储类型，分别是标准、低频、冷存，对应有效值：STANDARD | IA | ARCHIVE
                     */
                    .withStorageType(StorageType.STANDARD)
                    /**
                     * 配置进度监听
                     */
                    .setOnProgressListener((bytesWritten, contentLength1) -> JLog.D(TAG, String.format("[progress] = %d%% - [%d/%d]", (int) (bytesWritten * 1.f / contentLength1 * 100), bytesWritten, contentLength1)))
                    .execute();
            JLog.D(TAG, String.format("[res] = %s", (response == null ? "null" : response.toString())));
        } catch (UfileClientException | UfileServerException e) {
            e.printStackTrace();
        }
    }
}
