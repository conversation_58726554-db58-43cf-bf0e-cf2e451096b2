package ${packageName}.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.service.impl.SimpleServiceImpl;
#if($table.sub)
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.domain.condition.${ClassName}Condition;
import ${packageName}.service.I${ClassName}Service;


/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends SimpleServiceImpl<${ClassName}Mapper, ${ClassName}, ${ClassName}Condition>  implements I${ClassName}Service
{
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;


}
