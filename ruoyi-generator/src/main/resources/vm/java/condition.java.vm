package ${packageName}.domain.condition;

#foreach ($import in $importList)
import ${import};
#end
import com.ruoyi.common.core.domain.condition.SimpleCondition;
import com.ruoyi.common.core.wrapper.WhereType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel("${functionName}")
public class ${ClassName}Condition extends SimpleCondition
{

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    @ApiModelProperty("$column.columnComment")
    @WhereType
    private $column.javaType $column.javaField;
#end
#end

}
