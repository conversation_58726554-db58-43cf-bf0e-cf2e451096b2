-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}', '${parentMenuId}', '1', '${businessName}', '${moduleName}/${businessName}/index', 1, 0, 'C', '0', '0', '${permissionPrefix}:list', '#', 'admin', sysdate(), '', null, '${functionName}菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:export',       '#', 'admin', sysdate(), '', null, '');

-- 菜单多语言 SQL
-- 获取主菜单ID
SELECT @mainMenuId := LAST_INSERT_ID() - 5;

-- 插入主菜单多语言
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@mainMenuId, 'zh-CN', '${functionName}');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@mainMenuId, 'en-US', '${functionName}');

-- 插入按钮多语言
-- 查询按钮
SELECT @queryMenuId := @mainMenuId + 1;
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@queryMenuId, 'zh-CN', '${functionName}查询');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@queryMenuId, 'en-US', 'Query ${functionName}');

-- 新增按钮
SELECT @addMenuId := @mainMenuId + 2;
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@addMenuId, 'zh-CN', '${functionName}新增');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@addMenuId, 'en-US', 'Add ${functionName}');

-- 修改按钮
SELECT @editMenuId := @mainMenuId + 3;
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@editMenuId, 'zh-CN', '${functionName}修改');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@editMenuId, 'en-US', 'Edit ${functionName}');

-- 删除按钮
SELECT @removeMenuId := @mainMenuId + 4;
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@removeMenuId, 'zh-CN', '${functionName}删除');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@removeMenuId, 'en-US', 'Delete ${functionName}');

-- 导出按钮
SELECT @exportMenuId := @mainMenuId + 5;
insert into sys_menu_lang (menu_id, lang, menu_name)
values(@exportMenuId, 'zh-CN', '${functionName}导出');

insert into sys_menu_lang (menu_id, lang, menu_name)
values(@exportMenuId, 'en-US', 'Export ${functionName}');
