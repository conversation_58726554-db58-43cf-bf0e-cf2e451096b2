package com.ruoyi.generator.domain.condition;

import com.ruoyi.common.core.domain.condition.SimpleCondition;
import com.ruoyi.common.core.wrapper.WhereType;
import com.ruoyi.common.core.wrapper.WhereTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 代码生成业务对象 gen_table
 *
 * <AUTHOR>
 * @date 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel("代码生成业务")
public class GenTableCondition extends SimpleCondition
{

    @ApiModelProperty("编号")
    @WhereType
    private Long tableId;
    @ApiModelProperty("表名称")
    @WhereType
    private String tableName;
    @ApiModelProperty("表描述")
    @WhereType
    private String tableComment;
    @ApiModelProperty("关联子表的表名")
    @WhereType
    private String subTableName;
    @ApiModelProperty("子表关联的外键名")
    @WhereType
    private String subTableFkName;
    @ApiModelProperty("实体类名称")
    @WhereType
    private String className;
    @ApiModelProperty("使用的模板（crud单表操作 tree树表操作）")
    @WhereType
    private String tplCategory;
    @ApiModelProperty("生成包路径")
    @WhereType
    private String packageName;
    @ApiModelProperty("生成模块名")
    @WhereType
    private String moduleName;
    @ApiModelProperty("生成业务名")
    @WhereType
    private String businessName;
    @ApiModelProperty("生成功能名")
    @WhereType
    private String functionName;
    @ApiModelProperty("生成功能作者")
    @WhereType
    private String functionAuthor;
    @ApiModelProperty("生成代码方式（0zip压缩包 1自定义路径）")
    @WhereType
    private String genType;
    @ApiModelProperty("生成路径（不填默认项目路径）")
    @WhereType
    private String genPath;
    @ApiModelProperty("其它生成选项")
    @WhereType
    private String options;
    @ApiModelProperty("创建者")
    @WhereType
    private String createBy;
    @ApiModelProperty("创建时间")
    @WhereType
    private LocalDateTime createTime;
    @ApiModelProperty("更新者")
    @WhereType
    private String updateBy;
    @ApiModelProperty("更新时间")
    @WhereType
    private LocalDateTime updateTime;
    @ApiModelProperty("备注")
    @WhereType
    private String remark;

    @ApiModelProperty("开始时间")
    @WhereType(type = WhereTypeEnum.GE,column = "create_time")
    private LocalDateTime beginTime;

    @ApiModelProperty("开始时间")
    @WhereType(type = WhereTypeEnum.LE,column = "create_time")
    private LocalDateTime endTime;


}
