package com.ruoyi.generator.domain.condition;

import com.ruoyi.common.core.domain.condition.SimpleCondition;
import com.ruoyi.common.core.wrapper.WhereType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 代码生成业务字段对象 gen_table_column
 *
 * <AUTHOR>
 * @date 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel("代码生成业务字段")
public class GenTableColumnCondition extends SimpleCondition
{

    @ApiModelProperty("编号")
    @WhereType
    private Long columnId;
    @ApiModelProperty("归属表编号")
    @WhereType
    private String tableId;
    @ApiModelProperty("列名称")
    @WhereType
    private String columnName;
    @ApiModelProperty("列描述")
    @WhereType
    private String columnComment;
    @ApiModelProperty("列类型")
    @WhereType
    private String columnType;
    @ApiModelProperty("JAVA类型")
    @WhereType
    private String javaType;
    @ApiModelProperty("JAVA字段名")
    @WhereType
    private String javaField;
    @ApiModelProperty("是否主键（1是）")
    @WhereType
    private String isPk;
    @ApiModelProperty("是否自增（1是）")
    @WhereType
    private String isIncrement;
    @ApiModelProperty("是否必填（1是）")
    @WhereType
    private String isRequired;
    @ApiModelProperty("是否为插入字段（1是）")
    @WhereType
    private String isInsert;
    @ApiModelProperty("是否编辑字段（1是）")
    @WhereType
    private String isEdit;
    @ApiModelProperty("是否列表字段（1是）")
    @WhereType
    private String isList;
    @ApiModelProperty("是否查询字段（1是）")
    @WhereType
    private String isQuery;
    @ApiModelProperty("查询方式（等于、不等于、大于、小于、范围）")
    @WhereType
    private String queryType;
    @ApiModelProperty("显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）")
    @WhereType
    private String htmlType;
    @ApiModelProperty("字典类型")
    @WhereType
    private String dictType;
    @ApiModelProperty("排序")
    @WhereType
    private Long sort;
    @ApiModelProperty("创建者")
    @WhereType
    private String createBy;
    @ApiModelProperty("创建时间")
    @WhereType
    private LocalDateTime createTime;
    @ApiModelProperty("更新者")
    @WhereType
    private String updateBy;
    @ApiModelProperty("更新时间")
    @WhereType
    private LocalDateTime updateTime;

}
